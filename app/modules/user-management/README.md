# 用户角色权限管理模块

这是一个完整的用户角色权限管理系统，基于 React Router v7 构建，提供了用户管理、角色管理和权限管理的完整功能。

## 功能特性

### 🧑‍💼 用户管理
- ✅ 用户列表展示（分页、搜索）
- ✅ 创建新用户
- ✅ 编辑用户信息
- ✅ 删除用户（带确认）
- ✅ 用户状态管理（启用/禁用）
- ✅ 超级管理员设置
- ✅ 角色分配管理

### 🛡️ 角色管理
- ✅ 角色列表展示
- ✅ 创建新角色
- ✅ 编辑角色信息
- ✅ 删除角色（带验证）
- ✅ 权限配置
- ✅ 用户分配查看

### 🔑 权限管理
- ✅ 权限树形展示
- ✅ 按服务/模块分组
- ✅ 权限搜索和筛选
- ✅ 权限描述展示
- ✅ 权限统计信息

### 🎨 用户体验
- ✅ 响应式设计
- ✅ 中文界面
- ✅ 加载状态
- ✅ 错误处理
- ✅ 确认对话框
- ✅ 面包屑导航

## 技术栈

- **框架**: React Router v7
- **构建工具**: Vite
- **状态管理**: Zustand
- **数据获取**: SWR
- **UI组件**: Radix UI
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **表单**: React Hook Form
- **类型检查**: TypeScript

## 项目结构

```
app/modules/user-management/
├── components/           # UI组件
│   ├── UserList.tsx     # 用户列表
│   ├── UserForm.tsx     # 用户表单
│   ├── UserDetail.tsx   # 用户详情
│   ├── RoleList.tsx     # 角色列表
│   ├── RoleForm.tsx     # 角色表单
│   ├── RoleDetail.tsx   # 角色详情
│   ├── PermissionList.tsx        # 权限列表
│   ├── PermissionSelector.tsx    # 权限选择器
│   ├── Navigation.tsx            # 导航组件
│   ├── ErrorBoundary.tsx         # 错误边界
│   ├── LoadingStates.tsx         # 加载状态
│   └── ResponsiveHelpers.tsx     # 响应式辅助
├── pages/               # 页面组件
│   ├── DashboardPage.tsx         # 仪表板
│   ├── UserManagementPage.tsx    # 用户管理页
│   ├── RoleManagementPage.tsx    # 角色管理页
│   └── PermissionManagementPage.tsx # 权限管理页
├── stores/              # 状态管理
│   ├── userStore.ts     # 用户状态
│   └── roleStore.ts     # 角色状态
├── services/            # API服务
│   └── api.ts          # API接口
├── hooks/               # 自定义Hooks
│   └── usePermissions.ts # 权限相关Hooks
├── utils/               # 工具函数
│   └── index.ts        # 通用工具
├── types/               # 类型定义
│   └── index.ts        # TypeScript类型
└── index.ts            # 模块入口
```

## 路由配置

```
/user-management                    # 仪表板
/user-management/users              # 用户管理
/user-management/roles              # 角色管理
/user-management/permissions        # 权限管理
```

## 后端API接口

### 用户管理API
- `GET /api/users` - 获取用户列表
- `GET /api/users/:id` - 获取用户详情
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户
- `POST /api/users/assign-roles` - 分配角色

### 角色管理API
- `GET /api/roles` - 获取角色列表
- `GET /api/roles/all` - 获取所有角色
- `GET /api/roles/:id` - 获取角色详情
- `POST /api/roles` - 创建角色
- `PUT /api/roles/:id` - 更新角色
- `DELETE /api/roles/:id` - 删除角色

### 权限管理API
- `GET /api/permissions` - 获取可用权限

## 数据模型

### User（用户）
```typescript
interface User {
  id: number;
  username: string;
  real_name?: string;
  lark_open_id?: string;
  is_active: boolean;
  is_superuser: boolean;
  roles: Role[];
  created_at?: string;
  updated_at?: string;
}
```

### Role（角色）
```typescript
interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string; // 逗号分隔的权限字符串
  users?: User[];
  created_at?: string;
  updated_at?: string;
}
```

### Permission（权限）
```typescript
interface Permission {
  type: "api" | "field" | "record";
  service: string;
  module: string;
  action: "read" | "write" | "write.create" | "write.update" | "write.delete";
  name?: string;
  description?: string;
}
```

## 使用方法

### 1. 导入组件
```typescript
import { 
  UserList, 
  UserForm, 
  RoleList, 
  PermissionSelector 
} from '../modules/user-management';
```

### 2. 使用状态管理
```typescript
import { useUserStore, useRoleStore } from '../modules/user-management';

function MyComponent() {
  const { users, fetchUsers } = useUserStore();
  const { roles, fetchRoles } = useRoleStore();
  
  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, []);
  
  return (
    <div>
      {/* 组件内容 */}
    </div>
  );
}
```

### 3. 权限检查
```typescript
import { usePermissionChecker } from '../modules/user-management';

function ProtectedComponent() {
  const userPermissions = ['api:user:read', 'api:role:write'];
  const { hasPermission, hasAnyPermission } = usePermissionChecker(userPermissions);
  
  if (!hasPermission('api:user:read')) {
    return <div>无权限访问</div>;
  }
  
  return <div>受保护的内容</div>;
}
```

## 环境变量

```env
REACT_APP_API_BASE_URL=http://localhost:8000/api  # API基础URL
```

## 开发指南

### 添加新功能
1. 在 `types/index.ts` 中定义相关类型
2. 在 `services/api.ts` 中添加API接口
3. 在对应的store中添加状态管理逻辑
4. 创建UI组件
5. 添加路由配置

### 自定义样式
所有组件都使用Tailwind CSS类名，可以通过修改类名来自定义样式。

### 错误处理
使用 `ErrorBoundary` 组件包装需要错误处理的部分：

```typescript
import { ErrorBoundary } from '../modules/user-management';

<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>
```

## 注意事项

1. **权限格式**: 权限字符串格式为 `type:service:module:action`
2. **角色删除**: 只有未分配给任何用户的角色才能被删除
3. **超级管理员**: 超级管理员拥有所有权限，无需角色分配
4. **响应式设计**: 所有组件都支持移动端显示

## 许可证

MIT License
