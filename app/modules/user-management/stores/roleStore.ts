// 角色管理状态存储 - 使用Zustand
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { 
  Role, 
  Permission,
  PaginatedResponse, 
  PaginationParams, 
  TableState,
  LoadingState 
} from '../types';
import { roleApi, permissionApi } from '../services/api';

interface RoleStore {
  // 状态
  roles: Role[];
  allRoles: Role[]; // 用于下拉选择的所有角色
  selectedRole: Role | null;
  availablePermissions: Permission[];
  pagination: {
    total: number;
    page: number;
    size: number;
    pages: number;
  };
  tableState: TableState;
  loadingState: LoadingState;

  // 操作
  fetchRoles: (params?: PaginationParams) => Promise<void>;
  fetchAllRoles: () => Promise<void>;
  fetchRole: (id: number) => Promise<void>;
  fetchAvailablePermissions: () => Promise<void>;
  createRole: (roleData: any) => Promise<Role>;
  updateRole: (id: number, roleData: any) => Promise<Role>;
  deleteRole: (id: number) => Promise<void>;
  
  // 权限相关
  parseRolePermissions: (role: Role) => Permission[];
  
  // 表格状态管理
  setTableState: (state: Partial<TableState>) => void;
  resetTableState: () => void;
  
  // UI状态管理
  setSelectedRole: (role: Role | null) => void;
  setLoading: (loading: boolean, error?: string) => void;
  clearError: () => void;
}

const initialTableState: TableState = {
  page: 1,
  size: 10,
  search: '',
  sortBy: 'id',
  sortOrder: 'desc',
};

const initialLoadingState: LoadingState = {
  loading: false,
  error: undefined,
};

export const useRoleStore = create<RoleStore>()(
  immer((set, get) => ({
    // 初始状态
    roles: [],
    allRoles: [],
    selectedRole: null,
    availablePermissions: [],
    pagination: {
      total: 0,
      page: 1,
      size: 10,
      pages: 0,
    },
    tableState: initialTableState,
    loadingState: initialLoadingState,

    // 获取角色列表（分页）
    fetchRoles: async (params?: PaginationParams) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const queryParams = {
          ...params,
          page: params?.page || get().tableState.page,
          size: params?.size || get().tableState.size,
          search: params?.search || get().tableState.search,
        };

        const response = await roleApi.getRoles(queryParams);
        
        set((state) => {
          state.roles = response.items;
          state.pagination = {
            total: response.total,
            page: response.page,
            size: response.size,
            pages: response.pages,
          };
          state.loadingState.loading = false;
        });
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '获取角色列表失败';
        });
        throw error;
      }
    },

    // 获取所有角色（不分页）
    fetchAllRoles: async () => {
      try {
        const roles = await roleApi.getAllRoles();
        set((state) => {
          state.allRoles = roles;
        });
      } catch (error: any) {
        console.error('获取所有角色失败:', error);
      }
    },

    // 获取单个角色
    fetchRole: async (id: number) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const role = await roleApi.getRole(id);
        set((state) => {
          state.selectedRole = role;
          state.loadingState.loading = false;
        });
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '获取角色信息失败';
        });
        throw error;
      }
    },

    // 获取可用权限
    fetchAvailablePermissions: async () => {
      try {
        const permissions = await permissionApi.getAvailablePermissions();
        set((state) => {
          state.availablePermissions = permissions;
        });
      } catch (error: any) {
        console.error('获取可用权限失败:', error);
      }
    },

    // 创建角色
    createRole: async (roleData: any) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const newRole = await roleApi.createRole(roleData);
        set((state) => {
          state.roles.unshift(newRole);
          state.allRoles.unshift(newRole);
          state.pagination.total += 1;
          state.loadingState.loading = false;
        });
        return newRole;
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '创建角色失败';
        });
        throw error;
      }
    },

    // 更新角色
    updateRole: async (id: number, roleData: any) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const updatedRole = await roleApi.updateRole(id, roleData);
        set((state) => {
          const index = state.roles.findIndex(role => role.id === id);
          if (index !== -1) {
            state.roles[index] = updatedRole;
          }
          const allIndex = state.allRoles.findIndex(role => role.id === id);
          if (allIndex !== -1) {
            state.allRoles[allIndex] = updatedRole;
          }
          if (state.selectedRole?.id === id) {
            state.selectedRole = updatedRole;
          }
          state.loadingState.loading = false;
        });
        return updatedRole;
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '更新角色失败';
        });
        throw error;
      }
    },

    // 删除角色
    deleteRole: async (id: number) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        await roleApi.deleteRole(id);
        set((state) => {
          state.roles = state.roles.filter(role => role.id !== id);
          state.allRoles = state.allRoles.filter(role => role.id !== id);
          state.pagination.total -= 1;
          if (state.selectedRole?.id === id) {
            state.selectedRole = null;
          }
          state.loadingState.loading = false;
        });
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '删除角色失败';
        });
        throw error;
      }
    },

    // 解析角色权限
    parseRolePermissions: (role: Role) => {
      try {
        return permissionApi.parsePermissions(role.permissions);
      } catch (error) {
        console.error('解析角色权限失败:', error);
        return [];
      }
    },

    // 设置表格状态
    setTableState: (newState: Partial<TableState>) => {
      set((state) => {
        Object.assign(state.tableState, newState);
      });
    },

    // 重置表格状态
    resetTableState: () => {
      set((state) => {
        state.tableState = { ...initialTableState };
      });
    },

    // 设置选中的角色
    setSelectedRole: (role: Role | null) => {
      set((state) => {
        state.selectedRole = role;
      });
    },

    // 设置加载状态
    setLoading: (loading: boolean, error?: string) => {
      set((state) => {
        state.loadingState.loading = loading;
        state.loadingState.error = error;
      });
    },

    // 清除错误
    clearError: () => {
      set((state) => {
        state.loadingState.error = undefined;
      });
    },
  }))
);
