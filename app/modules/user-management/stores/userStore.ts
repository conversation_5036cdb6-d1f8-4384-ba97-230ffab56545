// 用户管理状态存储 - 使用Zustand
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { 
  User, 
  PaginatedResponse, 
  PaginationParams, 
  TableState,
  LoadingState 
} from '../types';
import { userApi } from '../services/api';

interface UserStore {
  // 状态
  users: User[];
  selectedUser: User | null;
  pagination: {
    total: number;
    page: number;
    size: number;
    pages: number;
  };
  tableState: TableState;
  loadingState: LoadingState;

  // 操作
  fetchUsers: (params?: PaginationParams) => Promise<void>;
  fetchUser: (id: number) => Promise<void>;
  createUser: (userData: any) => Promise<User>;
  updateUser: (id: number, userData: any) => Promise<User>;
  deleteUser: (id: number) => Promise<void>;
  assignRoles: (userId: number, roleIds: number[]) => Promise<void>;
  
  // 表格状态管理
  setTableState: (state: Partial<TableState>) => void;
  resetTableState: () => void;
  
  // UI状态管理
  setSelectedUser: (user: User | null) => void;
  setLoading: (loading: boolean, error?: string) => void;
  clearError: () => void;
}

const initialTableState: TableState = {
  page: 1,
  size: 10,
  search: '',
  sortBy: 'id',
  sortOrder: 'desc',
};

const initialLoadingState: LoadingState = {
  loading: false,
  error: undefined,
};

export const useUserStore = create<UserStore>()(
  immer((set, get) => ({
    // 初始状态
    users: [],
    selectedUser: null,
    pagination: {
      total: 0,
      page: 1,
      size: 10,
      pages: 0,
    },
    tableState: initialTableState,
    loadingState: initialLoadingState,

    // 获取用户列表
    fetchUsers: async (params?: PaginationParams) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const queryParams = {
          ...params,
          page: params?.page || get().tableState.page,
          size: params?.size || get().tableState.size,
          search: params?.search || get().tableState.search,
        };

        const response = await userApi.getUsers(queryParams);
        
        set((state) => {
          state.users = response.items;
          state.pagination = {
            total: response.total,
            page: response.page,
            size: response.size,
            pages: response.pages,
          };
          state.loadingState.loading = false;
        });
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '获取用户列表失败';
        });
        throw error;
      }
    },

    // 获取单个用户
    fetchUser: async (id: number) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const user = await userApi.getUser(id);
        set((state) => {
          state.selectedUser = user;
          state.loadingState.loading = false;
        });
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '获取用户信息失败';
        });
        throw error;
      }
    },

    // 创建用户
    createUser: async (userData: any) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const newUser = await userApi.createUser(userData);
        set((state) => {
          state.users.unshift(newUser);
          state.pagination.total += 1;
          state.loadingState.loading = false;
        });
        return newUser;
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '创建用户失败';
        });
        throw error;
      }
    },

    // 更新用户
    updateUser: async (id: number, userData: any) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        const updatedUser = await userApi.updateUser(id, userData);
        set((state) => {
          const index = state.users.findIndex(user => user.id === id);
          if (index !== -1) {
            state.users[index] = updatedUser;
          }
          if (state.selectedUser?.id === id) {
            state.selectedUser = updatedUser;
          }
          state.loadingState.loading = false;
        });
        return updatedUser;
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '更新用户失败';
        });
        throw error;
      }
    },

    // 删除用户
    deleteUser: async (id: number) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        await userApi.deleteUser(id);
        set((state) => {
          state.users = state.users.filter(user => user.id !== id);
          state.pagination.total -= 1;
          if (state.selectedUser?.id === id) {
            state.selectedUser = null;
          }
          state.loadingState.loading = false;
        });
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '删除用户失败';
        });
        throw error;
      }
    },

    // 分配角色
    assignRoles: async (userId: number, roleIds: number[]) => {
      set((state) => {
        state.loadingState.loading = true;
        state.loadingState.error = undefined;
      });

      try {
        await userApi.assignRoles({ user_id: userId, role_ids: roleIds });
        // 重新获取用户信息以更新角色
        await get().fetchUser(userId);
      } catch (error: any) {
        set((state) => {
          state.loadingState.loading = false;
          state.loadingState.error = error.response?.data?.message || '分配角色失败';
        });
        throw error;
      }
    },

    // 设置表格状态
    setTableState: (newState: Partial<TableState>) => {
      set((state) => {
        Object.assign(state.tableState, newState);
      });
    },

    // 重置表格状态
    resetTableState: () => {
      set((state) => {
        state.tableState = { ...initialTableState };
      });
    },

    // 设置选中的用户
    setSelectedUser: (user: User | null) => {
      set((state) => {
        state.selectedUser = user;
      });
    },

    // 设置加载状态
    setLoading: (loading: boolean, error?: string) => {
      set((state) => {
        state.loadingState.loading = loading;
        state.loadingState.error = error;
      });
    },

    // 清除错误
    clearError: () => {
      set((state) => {
        state.loadingState.error = undefined;
      });
    },
  }))
);
