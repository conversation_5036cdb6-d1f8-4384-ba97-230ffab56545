// 用户角色权限管理模块的TypeScript类型定义

export interface User {
  id: number;
  username: string;
  real_name?: string;
  lark_open_id?: string;
  is_active: boolean;
  is_superuser: boolean;
  roles: Role[];
  created_at?: string;
  updated_at?: string;
}

export interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string; // comma separated permission strings
  users?: User[];
  created_at?: string;
  updated_at?: string;
}

export interface UserRoleLink {
  user_id: number;
  role_id: number;
}

export type PermissionType = "api" | "field" | "record";
export type PermissionAction = "read" | "write" | "write.create" | "write.update" | "write.delete";

export interface Permission {
  type: PermissionType;
  service: string;
  module: string;
  action: PermissionAction;
  name?: string;
  description?: string;
}

// API请求和响应类型
export interface CreateUserRequest {
  username: string;
  real_name?: string;
  is_active?: boolean;
  is_superuser?: boolean;
  password?: string;
}

export interface UpdateUserRequest {
  username?: string;
  real_name?: string;
  is_active?: boolean;
  is_superuser?: boolean;
  password?: string;
}

export interface CreateRoleRequest {
  name: string;
  description: string;
  permissions: string[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];
}

export interface AssignRoleRequest {
  user_id: number;
  role_ids: number[];
}

// 分页相关类型
export interface PaginationParams {
  page?: number;
  size?: number;
  search?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// 表单状态类型
export interface UserFormData {
  username: string;
  real_name: string;
  is_active: boolean;
  is_superuser: boolean;
  password?: string;
  role_ids: number[];
}

export interface RoleFormData {
  name: string;
  description: string;
  permissions: string[];
}

// 权限分组类型
export interface PermissionGroup {
  service: string;
  modules: {
    [module: string]: Permission[];
  };
}

// API错误类型
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// 组件状态类型
export interface LoadingState {
  loading: boolean;
  error?: string;
}

export interface TableState {
  page: number;
  size: number;
  search: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 权限检查相关类型
export interface PermissionCheck {
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
}
