// 权限管理相关的自定义Hook
import { useMemo } from 'react';
import type { Permission, PermissionGroup } from '../types';
import { permissionApi } from '../services/api';

// 权限分组Hook
export function usePermissionGroups(permissions: Permission[]): PermissionGroup[] {
  return useMemo(() => {
    const groups: { [service: string]: PermissionGroup } = {};

    permissions.forEach(permission => {
      if (!groups[permission.service]) {
        groups[permission.service] = {
          service: permission.service,
          modules: {},
        };
      }

      if (!groups[permission.service].modules[permission.module]) {
        groups[permission.service].modules[permission.module] = [];
      }

      groups[permission.service].modules[permission.module].push(permission);
    });

    return Object.values(groups);
  }, [permissions]);
}

// 权限字符串解析Hook
export function usePermissionParser() {
  const parsePermissions = (permissionString: string): Permission[] => {
    try {
      return permissionApi.parsePermissions(permissionString);
    } catch (error) {
      console.error('解析权限字符串失败:', error);
      return [];
    }
  };

  const stringifyPermissions = (permissions: Permission[]): string => {
    return permissionApi.stringifyPermissions(permissions);
  };

  const formatPermissionDisplay = (permission: Permission): string => {
    return `${permission.service}.${permission.module}.${permission.action}`;
  };

  const getPermissionDescription = (permission: Permission): string => {
    if (permission.description) {
      return permission.description;
    }

    // 生成默认描述
    const actionMap: { [key: string]: string } = {
      'read': '读取',
      'write': '写入',
      'write.create': '创建',
      'write.update': '更新',
      'write.delete': '删除',
    };

    const typeMap: { [key: string]: string } = {
      'api': 'API',
      'field': '字段',
      'record': '记录',
    };

    return `${typeMap[permission.type] || permission.type} - ${permission.service}/${permission.module} - ${actionMap[permission.action] || permission.action}`;
  };

  return {
    parsePermissions,
    stringifyPermissions,
    formatPermissionDisplay,
    getPermissionDescription,
  };
}

// 权限检查Hook
export function usePermissionChecker(userPermissions: string[]) {
  const hasPermission = (permission: string): boolean => {
    return userPermissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => userPermissions.includes(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => userPermissions.includes(permission));
  };

  const hasServiceAccess = (service: string): boolean => {
    return userPermissions.some(permission => permission.startsWith(`api:${service}:`));
  };

  const hasModuleAccess = (service: string, module: string): boolean => {
    return userPermissions.some(permission => 
      permission.startsWith(`api:${service}:${module}:`)
    );
  };

  const getAccessibleActions = (service: string, module: string): string[] => {
    const prefix = `api:${service}:${module}:`;
    return userPermissions
      .filter(permission => permission.startsWith(prefix))
      .map(permission => permission.replace(prefix, ''));
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasServiceAccess,
    hasModuleAccess,
    getAccessibleActions,
  };
}

// 权限选择器状态管理Hook
export function usePermissionSelector(
  availablePermissions: Permission[],
  selectedPermissions: string[] = []
) {
  const permissionGroups = usePermissionGroups(availablePermissions);
  const { stringifyPermissions, parsePermissions } = usePermissionParser();

  const isPermissionSelected = (permission: Permission): boolean => {
    const permissionString = `${permission.type}:${permission.service}:${permission.module}:${permission.action}`;
    return selectedPermissions.includes(permissionString);
  };

  const isServiceSelected = (service: string): boolean => {
    const servicePermissions = availablePermissions.filter(p => p.service === service);
    return servicePermissions.every(permission => isPermissionSelected(permission));
  };

  const isModuleSelected = (service: string, module: string): boolean => {
    const modulePermissions = availablePermissions.filter(
      p => p.service === service && p.module === module
    );
    return modulePermissions.every(permission => isPermissionSelected(permission));
  };

  const getServiceSelectionState = (service: string): 'all' | 'partial' | 'none' => {
    const servicePermissions = availablePermissions.filter(p => p.service === service);
    const selectedCount = servicePermissions.filter(permission => 
      isPermissionSelected(permission)
    ).length;

    if (selectedCount === 0) return 'none';
    if (selectedCount === servicePermissions.length) return 'all';
    return 'partial';
  };

  const getModuleSelectionState = (service: string, module: string): 'all' | 'partial' | 'none' => {
    const modulePermissions = availablePermissions.filter(
      p => p.service === service && p.module === module
    );
    const selectedCount = modulePermissions.filter(permission => 
      isPermissionSelected(permission)
    ).length;

    if (selectedCount === 0) return 'none';
    if (selectedCount === modulePermissions.length) return 'all';
    return 'partial';
  };

  return {
    permissionGroups,
    isPermissionSelected,
    isServiceSelected,
    isModuleSelected,
    getServiceSelectionState,
    getModuleSelectionState,
  };
}
