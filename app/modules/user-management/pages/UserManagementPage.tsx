// 用户管理主页面
import { useNavigate } from "react-router";

import { UserList } from "../components/UserList";
import type { User } from "../types";

export function UserManagementPage() {
  const navigate = useNavigate();

  // 切换到编辑视图
  const handleEditUser = (user: User) => {
    navigate(`/user-management/users/${user.id}/edit`);
  };

  // 切换到详情视图
  const handleViewUser = (user: User) => {
    navigate(`/user-management/users/${user.id}`);
  };

  return <UserList onEditUser={handleEditUser} onViewUser={handleViewUser} />;
}
