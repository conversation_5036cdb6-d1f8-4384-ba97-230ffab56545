// 用户管理主页面
import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router';

import { UserList } from '../components/UserList';
import { UserForm } from '../components/UserForm';
import { UserDetail } from '../components/UserDetail';
import type { User } from '../types';

type ViewMode = 'list' | 'create' | 'edit' | 'detail';

export function UserManagementPage() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // 从URL参数获取当前视图模式
  React.useEffect(() => {
    const mode = searchParams.get('mode') as ViewMode;
    const userId = searchParams.get('userId');
    
    if (mode && ['list', 'create', 'edit', 'detail'].includes(mode)) {
      setViewMode(mode);
      
      if (userId && (mode === 'edit' || mode === 'detail')) {
        // 这里应该根据userId获取用户信息
        // 暂时使用mock数据，实际应该调用API
      }
    }
  }, [searchParams]);

  // 切换到列表视图
  const handleBackToList = () => {
    setViewMode('list');
    setSelectedUser(null);
    setSearchParams({});
  };

  // 切换到创建视图
  const handleCreateUser = () => {
    setViewMode('create');
    setSelectedUser(null);
    setSearchParams({ mode: 'create' });
  };

  // 切换到编辑视图
  const handleEditUser = (user: User) => {
    setViewMode('edit');
    setSelectedUser(user);
    setSearchParams({ mode: 'edit', userId: user.id.toString() });
  };

  // 切换到详情视图
  const handleViewUser = (user: User) => {
    setViewMode('detail');
    setSelectedUser(user);
    setSearchParams({ mode: 'detail', userId: user.id.toString() });
  };

  // 处理用户保存成功
  const handleUserSaved = (user: User) => {
    // 保存成功后返回列表或详情页
    if (viewMode === 'create') {
      handleViewUser(user);
    } else {
      handleBackToList();
    }
  };

  // 渲染当前视图
  const renderCurrentView = () => {
    switch (viewMode) {
      case 'list':
        return (
          <UserList
            onEditUser={handleEditUser}
            onViewUser={handleViewUser}
          />
        );

      case 'create':
        return (
          <UserForm
            onSave={handleUserSaved}
            onCancel={handleBackToList}
          />
        );

      case 'edit':
        return (
          <UserForm
            user={selectedUser}
            onSave={handleUserSaved}
            onCancel={handleBackToList}
          />
        );

      case 'detail':
        return selectedUser ? (
          <UserDetail
            userId={selectedUser.id}
            onEdit={handleEditUser}
          />
        ) : (
          <div>用户不存在</div>
        );

      default:
        return <UserList onEditUser={handleEditUser} onViewUser={handleViewUser} />;
    }
  };

  return (
    <div className="container mx-auto py-6">
      {renderCurrentView()}
    </div>
  );
}
