// 用户管理模块仪表板页面
import React, { useEffect } from 'react';
import { Link } from 'react-router';
import { 
  Users, 
  Shield, 
  Key, 
  UserCheck, 
  UserX,
  TrendingUp,
  Activity,
  Plus
} from 'lucide-react';

import { Button } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Badge } from '../../../components/ui/badge';
import { Skeleton } from '../../../components/ui/skeleton';
import { Avatar, AvatarFallback } from '../../../components/ui/avatar';

import { UserManagementQuickNav } from '../components/Navigation';
import { useUserStore } from '../stores/userStore';
import { useRoleStore } from '../stores/roleStore';
import { userUtils, roleUtils, formatUtils } from '../utils';

export function DashboardPage() {
  const { users, fetchUsers, loadingState: userLoadingState } = useUserStore();
  const { roles, fetchRoles, loadingState: roleLoadingState } = useRoleStore();

  // 获取数据
  useEffect(() => {
    fetchUsers({ size: 10 }); // 获取最近10个用户
    fetchRoles({ size: 10 }); // 获取最近10个角色
  }, [fetchUsers, fetchRoles]);

  // 计算统计数据
  const stats = React.useMemo(() => {
    const activeUsers = users.filter(user => user.is_active).length;
    const inactiveUsers = users.filter(user => !user.is_active).length;
    const superUsers = users.filter(user => user.is_superuser).length;
    const totalPermissions = roles.reduce((sum, role) => sum + roleUtils.getPermissionCount(role), 0);

    return {
      totalUsers: users.length,
      activeUsers,
      inactiveUsers,
      superUsers,
      totalRoles: roles.length,
      totalPermissions,
    };
  }, [users, roles]);

  // 渲染统计卡片
  const renderStatsCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总用户数</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalUsers}</div>
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <span className="flex items-center">
              <UserCheck className="mr-1 h-3 w-3" />
              {stats.activeUsers} 活跃
            </span>
            <span className="flex items-center">
              <UserX className="mr-1 h-3 w-3" />
              {stats.inactiveUsers} 禁用
            </span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总角色数</CardTitle>
          <Shield className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalRoles}</div>
          <p className="text-xs text-muted-foreground">
            系统角色配置
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总权限数</CardTitle>
          <Key className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalPermissions}</div>
          <p className="text-xs text-muted-foreground">
            已配置权限
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">超级管理员</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.superUsers}</div>
          <p className="text-xs text-muted-foreground">
            拥有全部权限
          </p>
        </CardContent>
      </Card>
    </div>
  );

  // 渲染最近用户
  const renderRecentUsers = () => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">最近用户</CardTitle>
        <Button variant="outline" size="sm" asChild>
          <Link to="/user-management/users">
            查看全部
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {userLoadingState.loading ? (
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        ) : users.length > 0 ? (
          <div className="space-y-3">
            {users.slice(0, 5).map((user) => (
              <div key={user.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="text-xs">
                      {userUtils.getDisplayName(user).charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="text-sm font-medium">
                      {userUtils.getDisplayName(user)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      @{user.username}
                    </div>
                  </div>
                </div>
                <Badge 
                  variant={userUtils.getStatusColor(user)}
                  className="text-xs"
                >
                  {userUtils.getStatusText(user)}
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Users className="mx-auto h-8 w-8 text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">暂无用户</p>
            <Button asChild className="mt-2" size="sm">
              <Link to="/user-management/users/new">
                <Plus className="mr-2 h-4 w-4" />
                创建用户
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );

  // 渲染最近角色
  const renderRecentRoles = () => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">最近角色</CardTitle>
        <Button variant="outline" size="sm" asChild>
          <Link to="/user-management/roles">
            查看全部
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {roleLoadingState.loading ? (
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-3">
                <Skeleton className="h-8 w-8 rounded" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        ) : roles.length > 0 ? (
          <div className="space-y-3">
            {roles.slice(0, 5).map((role) => (
              <div key={role.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-primary/10 rounded">
                    <Shield className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <div className="text-sm font-medium">{role.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatUtils.truncateText(role.description || '暂无描述', 30)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant="outline" className="text-xs">
                    {roleUtils.getPermissionCount(role)} 权限
                  </Badge>
                  <div className="text-xs text-muted-foreground mt-1">
                    {roleUtils.getUserCount(role)} 用户
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Shield className="mx-auto h-8 w-8 text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">暂无角色</p>
            <Button asChild className="mt-2" size="sm">
              <Link to="/user-management/roles/new">
                <Plus className="mr-2 h-4 w-4" />
                创建角色
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold">用户角色权限管理</h1>
        <p className="text-muted-foreground mt-2">
          管理系统用户账户、角色配置和权限分配
        </p>
      </div>

      {/* 统计卡片 */}
      {renderStatsCards()}

      {/* 快速导航 */}
      <div>
        <h2 className="text-xl font-semibold mb-4">快速导航</h2>
        <UserManagementQuickNav />
      </div>

      {/* 最近数据 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderRecentUsers()}
        {renderRecentRoles()}
      </div>
    </div>
  );
}
