// 角色管理主页面
import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router';

import { RoleList } from '../components/RoleList';
import { RoleForm } from '../components/RoleForm';
import { RoleDetail } from '../components/RoleDetail';
import type { Role } from '../types';

type ViewMode = 'list' | 'create' | 'edit' | 'detail';

export function RoleManagementPage() {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  // 从URL参数获取当前视图模式
  React.useEffect(() => {
    const mode = searchParams.get('mode') as ViewMode;
    const roleId = searchParams.get('roleId');
    
    if (mode && ['list', 'create', 'edit', 'detail'].includes(mode)) {
      setViewMode(mode);
      
      if (roleId && (mode === 'edit' || mode === 'detail')) {
        // 这里应该根据roleId获取角色信息
        // 暂时使用mock数据，实际应该调用API
      }
    }
  }, [searchParams]);

  // 切换到列表视图
  const handleBackToList = () => {
    setViewMode('list');
    setSelectedRole(null);
    setSearchParams({});
  };

  // 切换到创建视图
  const handleCreateRole = () => {
    setViewMode('create');
    setSelectedRole(null);
    setSearchParams({ mode: 'create' });
  };

  // 切换到编辑视图
  const handleEditRole = (role: Role) => {
    setViewMode('edit');
    setSelectedRole(role);
    setSearchParams({ mode: 'edit', roleId: role.id.toString() });
  };

  // 切换到详情视图
  const handleViewRole = (role: Role) => {
    setViewMode('detail');
    setSelectedRole(role);
    setSearchParams({ mode: 'detail', roleId: role.id.toString() });
  };

  // 处理角色保存成功
  const handleRoleSaved = (role: Role) => {
    // 保存成功后返回列表或详情页
    if (viewMode === 'create') {
      handleViewRole(role);
    } else {
      handleBackToList();
    }
  };

  // 渲染当前视图
  const renderCurrentView = () => {
    switch (viewMode) {
      case 'list':
        return (
          <RoleList
            onEditRole={handleEditRole}
            onViewRole={handleViewRole}
          />
        );

      case 'create':
        return (
          <RoleForm
            onSave={handleRoleSaved}
            onCancel={handleBackToList}
          />
        );

      case 'edit':
        return (
          <RoleForm
            role={selectedRole}
            onSave={handleRoleSaved}
            onCancel={handleBackToList}
          />
        );

      case 'detail':
        return selectedRole ? (
          <RoleDetail
            roleId={selectedRole.id}
            onEdit={handleEditRole}
          />
        ) : (
          <div>角色不存在</div>
        );

      default:
        return <RoleList onEditRole={handleEditRole} onViewRole={handleViewRole} />;
    }
  };

  return (
    <div className="container mx-auto py-6">
      {renderCurrentView()}
    </div>
  );
}
