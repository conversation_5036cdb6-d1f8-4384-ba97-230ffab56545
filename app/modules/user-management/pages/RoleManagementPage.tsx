// 角色管理主页面
import { useNavigate } from "react-router";

import { RoleList } from "../components/RoleList";
import type { Role } from "../types";

export function RoleManagementPage() {
  const navigate = useNavigate();

  // 切换到编辑视图
  const handleEditRole = (role: Role) => {
    navigate(`/user-management/roles/${role.id}/edit`);
  };

  // 切换到详情视图
  const handleViewRole = (role: Role) => {
    navigate(`/user-management/roles/${role.id}`);
  };

  return <RoleList onEditRole={handleEditRole} onViewRole={handleViewRole} />;
}
