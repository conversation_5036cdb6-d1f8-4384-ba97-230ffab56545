// 用户角色权限管理模块入口文件

// 类型定义
export * from './types';

// 服务层
export * from './services/api';

// 状态管理
export * from './stores/userStore';
export * from './stores/roleStore';

// 自定义Hooks
export * from './hooks/usePermissions';

// 工具函数
export * from './utils';

// 组件
export { UserList } from './components/UserList';
export { UserForm } from './components/UserForm';
export { UserDetail } from './components/UserDetail';
export { RoleList } from './components/RoleList';
export { RoleForm } from './components/RoleForm';
export { RoleDetail } from './components/RoleDetail';
export { PermissionList } from './components/PermissionList';
export { PermissionSelector } from './components/PermissionSelector';
export { 
  UserManagementBreadcrumb,
  UserManagementSidebar,
  UserManagementTopNav,
  UserManagementQuickNav 
} from './components/Navigation';
export { ErrorBoundary, useAsyncError } from './components/ErrorBoundary';
export { 
  LoadingSpinner,
  PageLoading,
  UserListSkeleton,
  RoleListSkeleton,
  PermissionTreeSkeleton,
  FormSkeleton,
  StatsSkeleton,
  InlineLoading,
  ButtonLoading,
  UserManagementLoading 
} from './components/LoadingStates';
export {
  ResponsiveContainer,
  ResponsiveGrid,
  MobileSidebar,
  ResponsiveTable,
  ResponsiveCardLayout,
  ResponsiveActionBar,
  ResponsiveFormLayout,
  ResponsiveModalContent,
  ResponsivePagination
} from './components/ResponsiveHelpers';

// 页面组件
export { DashboardPage } from './pages/DashboardPage';
export { UserManagementPage } from './pages/UserManagementPage';
export { RoleManagementPage } from './pages/RoleManagementPage';
export { PermissionManagementPage } from './pages/PermissionManagementPage';
