// 权限选择器组件
import React, { useState, useMemo } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Check, 
  Minus,
  Shield,
  Server,
  Database,
  Search
} from 'lucide-react';

import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Checkbox } from '../../../components/ui/checkbox';
import { Badge } from '../../../components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../../../components/ui/collapsible';
import { Separator } from '../../../components/ui/separator';

import { usePermissionGroups, usePermissionParser } from '../hooks/usePermissions';
import type { Permission } from '../types';

interface PermissionSelectorProps {
  availablePermissions: Permission[];
  selectedPermissions: string[];
  onPermissionChange: (permissions: string[]) => void;
  disabled?: boolean;
}

export function PermissionSelector({
  availablePermissions,
  selectedPermissions,
  onPermissionChange,
  disabled = false,
}: PermissionSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedServices, setExpandedServices] = useState<Set<string>>(new Set());
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());

  const permissionGroups = usePermissionGroups(availablePermissions);
  const { getPermissionDescription } = usePermissionParser();

  // 过滤权限组
  const filteredGroups = useMemo(() => {
    if (!searchTerm) return permissionGroups;

    return permissionGroups.map(group => ({
      ...group,
      modules: Object.fromEntries(
        Object.entries(group.modules).filter(([moduleName, permissions]) => {
          const moduleMatches = moduleName.toLowerCase().includes(searchTerm.toLowerCase());
          const serviceMatches = group.service.toLowerCase().includes(searchTerm.toLowerCase());
          const permissionMatches = permissions.some(p => 
            p.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
            getPermissionDescription(p).toLowerCase().includes(searchTerm.toLowerCase())
          );
          return moduleMatches || serviceMatches || permissionMatches;
        })
      ),
    })).filter(group => Object.keys(group.modules).length > 0);
  }, [permissionGroups, searchTerm, getPermissionDescription]);

  // 权限选择状态检查
  const isPermissionSelected = (permission: Permission): boolean => {
    const permissionString = `${permission.type}:${permission.service}:${permission.module}:${permission.action}`;
    return selectedPermissions.includes(permissionString);
  };

  const getServiceSelectionState = (service: string): 'all' | 'partial' | 'none' => {
    const servicePermissions = availablePermissions.filter(p => p.service === service);
    const selectedCount = servicePermissions.filter(p => isPermissionSelected(p)).length;

    if (selectedCount === 0) return 'none';
    if (selectedCount === servicePermissions.length) return 'all';
    return 'partial';
  };

  const getModuleSelectionState = (service: string, module: string): 'all' | 'partial' | 'none' => {
    const modulePermissions = availablePermissions.filter(
      p => p.service === service && p.module === module
    );
    const selectedCount = modulePermissions.filter(p => isPermissionSelected(p)).length;

    if (selectedCount === 0) return 'none';
    if (selectedCount === modulePermissions.length) return 'all';
    return 'partial';
  };

  // 权限选择处理
  const handlePermissionToggle = (permission: Permission, checked: boolean) => {
    const permissionString = `${permission.type}:${permission.service}:${permission.module}:${permission.action}`;
    
    let newPermissions: string[];
    if (checked) {
      newPermissions = [...selectedPermissions, permissionString];
    } else {
      newPermissions = selectedPermissions.filter(p => p !== permissionString);
    }
    
    onPermissionChange(newPermissions);
  };

  const handleServiceToggle = (service: string, checked: boolean) => {
    const servicePermissions = availablePermissions.filter(p => p.service === service);
    const servicePermissionStrings = servicePermissions.map(p => 
      `${p.type}:${p.service}:${p.module}:${p.action}`
    );

    let newPermissions: string[];
    if (checked) {
      // 添加所有服务权限
      newPermissions = [...new Set([...selectedPermissions, ...servicePermissionStrings])];
    } else {
      // 移除所有服务权限
      newPermissions = selectedPermissions.filter(p => 
        !servicePermissionStrings.includes(p)
      );
    }
    
    onPermissionChange(newPermissions);
  };

  const handleModuleToggle = (service: string, module: string, checked: boolean) => {
    const modulePermissions = availablePermissions.filter(
      p => p.service === service && p.module === module
    );
    const modulePermissionStrings = modulePermissions.map(p => 
      `${p.type}:${p.service}:${p.module}:${p.action}`
    );

    let newPermissions: string[];
    if (checked) {
      // 添加所有模块权限
      newPermissions = [...new Set([...selectedPermissions, ...modulePermissionStrings])];
    } else {
      // 移除所有模块权限
      newPermissions = selectedPermissions.filter(p => 
        !modulePermissionStrings.includes(p)
      );
    }
    
    onPermissionChange(newPermissions);
  };

  // 展开/折叠处理
  const toggleServiceExpanded = (service: string) => {
    const newExpanded = new Set(expandedServices);
    if (newExpanded.has(service)) {
      newExpanded.delete(service);
    } else {
      newExpanded.add(service);
    }
    setExpandedServices(newExpanded);
  };

  const toggleModuleExpanded = (service: string, module: string) => {
    const key = `${service}:${module}`;
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedModules(newExpanded);
  };

  // 渲染选择状态图标
  const renderSelectionIcon = (state: 'all' | 'partial' | 'none') => {
    switch (state) {
      case 'all':
        return <Check className="h-4 w-4 text-primary" />;
      case 'partial':
        return <Minus className="h-4 w-4 text-primary" />;
      case 'none':
      default:
        return null;
    }
  };

  // 获取操作类型的中文名称
  const getActionDisplayName = (action: string): string => {
    const actionMap: { [key: string]: string } = {
      'read': '读取',
      'write': '写入',
      'write.create': '创建',
      'write.update': '更新',
      'write.delete': '删除',
    };
    return actionMap[action] || action;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          权限配置
        </CardTitle>
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            已选择 {selectedPermissions.length} / {availablePermissions.length} 个权限
          </p>
          <Badge variant="outline">
            {Math.round((selectedPermissions.length / availablePermissions.length) * 100)}% 完成
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 搜索框 */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="搜索权限..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
            disabled={disabled}
          />
        </div>

        {/* 权限树 */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {filteredGroups.map((group) => {
            const serviceState = getServiceSelectionState(group.service);
            const isServiceExpanded = expandedServices.has(group.service);

            return (
              <div key={group.service} className="border rounded-lg">
                <Collapsible
                  open={isServiceExpanded}
                  onOpenChange={() => toggleServiceExpanded(group.service)}
                >
                  <CollapsibleTrigger asChild>
                    <div className="flex items-center justify-between p-3 hover:bg-muted/50 cursor-pointer">
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-2">
                          {isServiceExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          <Server className="h-4 w-4 text-muted-foreground" />
                        </div>
                        <Checkbox
                          checked={serviceState === 'all'}
                          ref={(el) => {
                            if (el) {
                              el.indeterminate = serviceState === 'partial';
                            }
                          }}
                          onCheckedChange={(checked) => 
                            handleServiceToggle(group.service, checked as boolean)
                          }
                          disabled={disabled}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <span className="font-medium">{group.service}</span>
                        <Badge variant="outline" className="text-xs">
                          {Object.values(group.modules).flat().length} 个权限
                        </Badge>
                      </div>
                      {renderSelectionIcon(serviceState)}
                    </div>
                  </CollapsibleTrigger>

                  <CollapsibleContent>
                    <div className="px-3 pb-3 space-y-2">
                      {Object.entries(group.modules).map(([moduleName, permissions]) => {
                        const moduleState = getModuleSelectionState(group.service, moduleName);
                        const moduleKey = `${group.service}:${moduleName}`;
                        const isModuleExpanded = expandedModules.has(moduleKey);

                        return (
                          <div key={moduleName} className="border-l-2 border-muted ml-4 pl-4">
                            <Collapsible
                              open={isModuleExpanded}
                              onOpenChange={() => toggleModuleExpanded(group.service, moduleName)}
                            >
                              <CollapsibleTrigger asChild>
                                <div className="flex items-center justify-between py-2 hover:bg-muted/30 cursor-pointer rounded px-2">
                                  <div className="flex items-center space-x-2">
                                    <div className="flex items-center space-x-2">
                                      {isModuleExpanded ? (
                                        <ChevronDown className="h-3 w-3" />
                                      ) : (
                                        <ChevronRight className="h-3 w-3" />
                                      )}
                                      <Database className="h-3 w-3 text-muted-foreground" />
                                    </div>
                                    <Checkbox
                                      checked={moduleState === 'all'}
                                      ref={(el) => {
                                        if (el) {
                                          el.indeterminate = moduleState === 'partial';
                                        }
                                      }}
                                      onCheckedChange={(checked) => 
                                        handleModuleToggle(group.service, moduleName, checked as boolean)
                                      }
                                      disabled={disabled}
                                      onClick={(e) => e.stopPropagation()}
                                    />
                                    <span className="text-sm font-medium">{moduleName}</span>
                                    <Badge variant="outline" className="text-xs">
                                      {permissions.length} 个权限
                                    </Badge>
                                  </div>
                                  {renderSelectionIcon(moduleState)}
                                </div>
                              </CollapsibleTrigger>

                              <CollapsibleContent>
                                <div className="ml-6 space-y-1">
                                  {permissions.map((permission) => (
                                    <div key={`${permission.type}:${permission.service}:${permission.module}:${permission.action}`} 
                                         className="flex items-center space-x-2 py-1">
                                      <Checkbox
                                        checked={isPermissionSelected(permission)}
                                        onCheckedChange={(checked) => 
                                          handlePermissionToggle(permission, checked as boolean)
                                        }
                                        disabled={disabled}
                                      />
                                      <div className="flex-1">
                                        <div className="text-sm">
                                          {getActionDisplayName(permission.action)}
                                        </div>
                                        <div className="text-xs text-muted-foreground">
                                          {getPermissionDescription(permission)}
                                        </div>
                                      </div>
                                      <Badge variant="outline" className="text-xs">
                                        {permission.type}
                                      </Badge>
                                    </div>
                                  ))}
                                </div>
                              </CollapsibleContent>
                            </Collapsible>
                          </div>
                        );
                      })}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            );
          })}
        </div>

        {/* 空状态 */}
        {filteredGroups.length === 0 && (
          <div className="text-center py-8">
            <Shield className="mx-auto h-8 w-8 text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">
              {searchTerm ? '未找到匹配的权限' : '暂无可用权限'}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
