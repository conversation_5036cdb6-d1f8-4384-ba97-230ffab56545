// 用户列表组件
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  <PERSON>r<PERSON>heck, 
  UserX, 
  Shield,
  MoreHorizontal 
} from 'lucide-react';

import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../../../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../../components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../../components/ui/alert-dialog';
import { Skeleton } from '../../../components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';

import { useUserStore } from '../stores/userStore';
import { userUtils, formatUtils } from '../utils';
import type { User } from '../types';

interface UserListProps {
  onEditUser?: (user: User) => void;
  onViewUser?: (user: User) => void;
}

export function UserList({ onEditUser, onViewUser }: UserListProps) {
  const {
    users,
    pagination,
    tableState,
    loadingState,
    fetchUsers,
    deleteUser,
    setTableState,
  } = useUserStore();

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [searchInput, setSearchInput] = useState(tableState.search);

  // 初始化加载用户列表
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchInput(value);
    setTableState({ search: value, page: 1 });
    fetchUsers({ search: value, page: 1 });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setTableState({ page });
    fetchUsers({ page });
  };

  // 处理删除用户
  const handleDeleteUser = async () => {
    if (!userToDelete) return;
    
    try {
      await deleteUser(userToDelete.id);
      setDeleteDialogOpen(false);
      setUserToDelete(null);
    } catch (error) {
      console.error('删除用户失败:', error);
    }
  };

  // 打开删除确认对话框
  const openDeleteDialog = (user: User) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  // 渲染用户状态徽章
  const renderUserStatus = (user: User) => {
    const statusText = userUtils.getStatusText(user);
    const statusColor = userUtils.getStatusColor(user);
    
    return (
      <Badge variant={statusColor} className="text-xs">
        {statusText}
      </Badge>
    );
  };

  // 渲染用户角色
  const renderUserRoles = (user: User) => {
    const roleNames = userUtils.getRoleNames(user);
    
    if (roleNames.length === 0) {
      return <span className="text-muted-foreground text-sm">无角色</span>;
    }

    return (
      <div className="flex flex-wrap gap-1">
        {roleNames.slice(0, 2).map((roleName) => (
          <Badge key={roleName} variant="outline" className="text-xs">
            {roleName}
          </Badge>
        ))}
        {roleNames.length > 2 && (
          <Badge variant="outline" className="text-xs">
            +{roleNames.length - 2}
          </Badge>
        )}
      </div>
    );
  };

  // 渲染操作菜单
  const renderActionMenu = (user: User) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onViewUser?.(user)}>
          <UserCheck className="mr-2 h-4 w-4" />
          查看详情
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onEditUser?.(user)}>
          <Edit className="mr-2 h-4 w-4" />
          编辑用户
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={() => openDeleteDialog(user)}
          className="text-destructive"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          删除用户
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // 渲染加载骨架
  const renderSkeleton = () => (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">用户管理</h1>
          <p className="text-muted-foreground">
            管理系统用户账户和权限分配
          </p>
        </div>
        <Button asChild>
          <Link to="/user-management/users/new">
            <Plus className="mr-2 h-4 w-4" />
            新建用户
          </Link>
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">用户列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索用户名或真实姓名..."
                value={searchInput}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* 用户表格 */}
          {loadingState.loading ? (
            renderSkeleton()
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>用户信息</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>角色</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">
                            {userUtils.getDisplayName(user)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            @{user.username}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {renderUserStatus(user)}
                      </TableCell>
                      <TableCell>
                        {renderUserRoles(user)}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatUtils.formatDate(user.created_at || '')}
                      </TableCell>
                      <TableCell className="text-right">
                        {renderActionMenu(user)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    共 {pagination.total} 个用户，第 {pagination.page} / {pagination.pages} 页
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.pages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 错误提示 */}
          {loadingState.error && (
            <div className="text-center py-8">
              <p className="text-destructive">{loadingState.error}</p>
              <Button 
                variant="outline" 
                onClick={() => fetchUsers()}
                className="mt-2"
              >
                重试
              </Button>
            </div>
          )}

          {/* 空状态 */}
          {!loadingState.loading && users.length === 0 && !loadingState.error && (
            <div className="text-center py-8">
              <UserX className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">暂无用户</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                开始创建第一个用户账户
              </p>
              <Button asChild className="mt-4">
                <Link to="/user-management/users/new">
                  <Plus className="mr-2 h-4 w-4" />
                  新建用户
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除用户</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除用户 "{userToDelete?.username}" 吗？
              此操作不可撤销，用户的所有数据将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteUser}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
