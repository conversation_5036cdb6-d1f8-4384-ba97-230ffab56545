// 权限列表组件
import React, { useEffect, useState } from 'react';
import { 
  Search, 
  Shield, 
  Server, 
  Database, 
  Key,
  Filter,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../../../components/ui/collapsible';
import { Separator } from '../../../components/ui/separator';
import { Skeleton } from '../../../components/ui/skeleton';

import { useRoleStore } from '../stores/roleStore';
import { usePermissionGroups, usePermissionParser } from '../hooks/usePermissions';
import type { Permission, PermissionType, PermissionAction } from '../types';

export function PermissionList() {
  const { availablePermissions, loadingState, fetchAvailablePermissions } = useRoleStore();
  const { getPermissionDescription } = usePermissionParser();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<PermissionType | 'all'>('all');
  const [actionFilter, setActionFilter] = useState<PermissionAction | 'all'>('all');
  const [expandedServices, setExpandedServices] = useState<Set<string>>(new Set());

  // 获取可用权限
  useEffect(() => {
    fetchAvailablePermissions();
  }, [fetchAvailablePermissions]);

  // 过滤权限
  const filteredPermissions = React.useMemo(() => {
    return availablePermissions.filter(permission => {
      const matchesSearch = !searchTerm || 
        permission.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        getPermissionDescription(permission).toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = typeFilter === 'all' || permission.type === typeFilter;
      const matchesAction = actionFilter === 'all' || permission.action === actionFilter;

      return matchesSearch && matchesType && matchesAction;
    });
  }, [availablePermissions, searchTerm, typeFilter, actionFilter, getPermissionDescription]);

  const permissionGroups = usePermissionGroups(filteredPermissions);

  // 获取统计信息
  const stats = React.useMemo(() => {
    const services = new Set(availablePermissions.map(p => p.service));
    const modules = new Set(availablePermissions.map(p => `${p.service}:${p.module}`));
    const types = new Set(availablePermissions.map(p => p.type));
    const actions = new Set(availablePermissions.map(p => p.action));

    return {
      total: availablePermissions.length,
      services: services.size,
      modules: modules.size,
      types: types.size,
      actions: actions.size,
      filtered: filteredPermissions.length,
    };
  }, [availablePermissions, filteredPermissions]);

  // 展开/折叠服务
  const toggleServiceExpanded = (service: string) => {
    const newExpanded = new Set(expandedServices);
    if (newExpanded.has(service)) {
      newExpanded.delete(service);
    } else {
      newExpanded.add(service);
    }
    setExpandedServices(newExpanded);
  };

  // 展开所有服务
  const expandAllServices = () => {
    const allServices = new Set(permissionGroups.map(g => g.service));
    setExpandedServices(allServices);
  };

  // 折叠所有服务
  const collapseAllServices = () => {
    setExpandedServices(new Set());
  };

  // 获取权限类型的中文名称
  const getTypeDisplayName = (type: PermissionType): string => {
    const typeMap: { [key in PermissionType]: string } = {
      'api': 'API接口',
      'field': '字段权限',
      'record': '记录权限',
    };
    return typeMap[type];
  };

  // 获取操作类型的中文名称
  const getActionDisplayName = (action: PermissionAction): string => {
    const actionMap: { [key in PermissionAction]: string } = {
      'read': '读取',
      'write': '写入',
      'write.create': '创建',
      'write.update': '更新',
      'write.delete': '删除',
    };
    return actionMap[action];
  };

  // 渲染统计卡片
  const renderStatsCards = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{stats.total}</div>
            <div className="text-xs text-muted-foreground">总权限数</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{stats.services}</div>
            <div className="text-xs text-muted-foreground">服务数</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{stats.modules}</div>
            <div className="text-xs text-muted-foreground">模块数</div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{stats.filtered}</div>
            <div className="text-xs text-muted-foreground">筛选结果</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // 渲染加载骨架
  const renderSkeleton = () => (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index}>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
        </Card>
      ))}
    </div>
  );

  if (loadingState.loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">权限管理</h1>
          <p className="text-muted-foreground">查看和管理系统权限配置</p>
        </div>
        {renderSkeleton()}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold">权限管理</h1>
        <p className="text-muted-foreground">
          查看和管理系统权限配置
        </p>
      </div>

      {/* 统计卡片 */}
      {renderStatsCards()}

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Key className="mr-2 h-5 w-5" />
              权限列表
            </span>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={expandAllServices}
              >
                展开全部
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={collapseAllServices}
              >
                折叠全部
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* 筛选控件 */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索权限..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as any)}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="权限类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有类型</SelectItem>
                <SelectItem value="api">API接口</SelectItem>
                <SelectItem value="field">字段权限</SelectItem>
                <SelectItem value="record">记录权限</SelectItem>
              </SelectContent>
            </Select>

            <Select value={actionFilter} onValueChange={(value) => setActionFilter(value as any)}>
              <SelectTrigger className="w-full md:w-40">
                <SelectValue placeholder="操作类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有操作</SelectItem>
                <SelectItem value="read">读取</SelectItem>
                <SelectItem value="write">写入</SelectItem>
                <SelectItem value="write.create">创建</SelectItem>
                <SelectItem value="write.update">更新</SelectItem>
                <SelectItem value="write.delete">删除</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 权限树 */}
          <div className="space-y-4">
            {permissionGroups.map((group) => {
              const isExpanded = expandedServices.has(group.service);
              
              return (
                <Card key={group.service} className="border">
                  <Collapsible
                    open={isExpanded}
                    onOpenChange={() => toggleServiceExpanded(group.service)}
                  >
                    <CollapsibleTrigger asChild>
                      <CardHeader className="cursor-pointer hover:bg-muted/50">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {isExpanded ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                            <Server className="h-4 w-4 text-muted-foreground" />
                            <CardTitle className="text-lg">{group.service}</CardTitle>
                            <Badge variant="outline">
                              {Object.values(group.modules).flat().length} 个权限
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                    </CollapsibleTrigger>

                    <CollapsibleContent>
                      <CardContent className="pt-0">
                        <div className="space-y-4">
                          {Object.entries(group.modules).map(([moduleName, permissions]) => (
                            <div key={moduleName} className="border-l-2 border-muted pl-4">
                              <div className="flex items-center space-x-2 mb-3">
                                <Database className="h-4 w-4 text-muted-foreground" />
                                <h4 className="font-medium">{moduleName}</h4>
                                <Badge variant="outline" className="text-xs">
                                  {permissions.length} 个权限
                                </Badge>
                              </div>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                {permissions.map((permission, index) => (
                                  <div key={index} className="p-3 border rounded-lg bg-muted/30">
                                    <div className="flex items-center justify-between mb-2">
                                      <Badge variant="secondary" className="text-xs">
                                        {getActionDisplayName(permission.action)}
                                      </Badge>
                                      <Badge variant="outline" className="text-xs">
                                        {getTypeDisplayName(permission.type)}
                                      </Badge>
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {getPermissionDescription(permission)}
                                    </div>
                                    <div className="text-xs font-mono text-muted-foreground mt-1">
                                      {`${permission.type}:${permission.service}:${permission.module}:${permission.action}`}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              );
            })}
          </div>

          {/* 空状态 */}
          {permissionGroups.length === 0 && (
            <div className="text-center py-8">
              <Key className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">
                {searchTerm || typeFilter !== 'all' || actionFilter !== 'all' 
                  ? '未找到匹配的权限' 
                  : '暂无权限配置'
                }
              </h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchTerm || typeFilter !== 'all' || actionFilter !== 'all'
                  ? '请尝试调整搜索条件或筛选器'
                  : '系统中暂无可用权限'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
