// 用户角色权限管理模块导航组件
import React from 'react';
import { Link, useLocation } from 'react-router';
import { 
  Users, 
  Shield, 
  Key, 
  Settings,
  Home,
  ChevronRight
} from 'lucide-react';

import { But<PERSON> } from '../../../components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';
import { Separator } from '../../../components/ui/separator';
import { Badge } from '../../../components/ui/badge';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../../../components/ui/breadcrumb';

interface NavigationItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  badge?: string;
}

const navigationItems: NavigationItem[] = [
  {
    path: '/user-management/users',
    label: '用户管理',
    icon: Users,
    description: '管理系统用户账户',
    badge: 'NEW',
  },
  {
    path: '/user-management/roles',
    label: '角色管理',
    icon: Shield,
    description: '管理用户角色和权限',
  },
  {
    path: '/user-management/permissions',
    label: '权限管理',
    icon: Key,
    description: '查看系统权限配置',
  },
];

// 面包屑导航组件
export function UserManagementBreadcrumb() {
  const location = useLocation();
  const pathname = location.pathname;

  const getBreadcrumbItems = () => {
    const items = [
      { label: '首页', path: '/' },
      { label: '用户管理', path: '/user-management' },
    ];

    if (pathname.includes('/users')) {
      items.push({ label: '用户管理', path: '/user-management/users' });
      
      if (pathname.includes('/new')) {
        items.push({ label: '新建用户', path: '' });
      } else if (pathname.includes('/edit')) {
        items.push({ label: '编辑用户', path: '' });
      } else if (pathname.match(/\/users\/\d+$/)) {
        items.push({ label: '用户详情', path: '' });
      }
    } else if (pathname.includes('/roles')) {
      items.push({ label: '角色管理', path: '/user-management/roles' });
      
      if (pathname.includes('/new')) {
        items.push({ label: '新建角色', path: '' });
      } else if (pathname.includes('/edit')) {
        items.push({ label: '编辑角色', path: '' });
      } else if (pathname.match(/\/roles\/\d+$/)) {
        items.push({ label: '角色详情', path: '' });
      }
    } else if (pathname.includes('/permissions')) {
      items.push({ label: '权限管理', path: '/user-management/permissions' });
    }

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={index}>
            <BreadcrumbItem>
              {index === breadcrumbItems.length - 1 || !item.path ? (
                <BreadcrumbPage>{item.label}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link to={item.path}>{item.label}</Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < breadcrumbItems.length - 1 && <BreadcrumbSeparator />}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

// 侧边导航组件
export function UserManagementSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;

  const isActive = (path: string) => {
    return currentPath.startsWith(path);
  };

  return (
    <div className="w-64 space-y-4">
      <Card>
        <CardContent className="p-4">
          <div className="space-y-1">
            <h3 className="font-semibold text-sm">用户角色权限管理</h3>
            <p className="text-xs text-muted-foreground">
              管理系统用户、角色和权限配置
            </p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-2">
          <nav className="space-y-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <Button
                  key={item.path}
                  variant={active ? "secondary" : "ghost"}
                  className="w-full justify-start h-auto p-3"
                  asChild
                >
                  <Link to={item.path}>
                    <div className="flex items-center space-x-3 w-full">
                      <Icon className="h-4 w-4 shrink-0" />
                      <div className="flex-1 text-left">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">{item.label}</span>
                          {item.badge && (
                            <Badge variant="outline" className="text-xs">
                              {item.badge}
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mt-0.5">
                          {item.description}
                        </p>
                      </div>
                      {active && <ChevronRight className="h-3 w-3" />}
                    </div>
                  </Link>
                </Button>
              );
            })}
          </nav>
        </CardContent>
      </Card>
    </div>
  );
}

// 顶部导航组件
export function UserManagementTopNav() {
  const location = useLocation();
  const currentPath = location.pathname;

  const isActive = (path: string) => {
    return currentPath.startsWith(path);
  };

  return (
    <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto">
        <div className="flex h-14 items-center space-x-4">
          <Link to="/user-management" className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span className="font-semibold">用户管理</span>
          </Link>
          
          <Separator orientation="vertical" className="h-6" />
          
          <nav className="flex items-center space-x-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <Button
                  key={item.path}
                  variant={active ? "secondary" : "ghost"}
                  size="sm"
                  className="h-8"
                  asChild
                >
                  <Link to={item.path}>
                    <Icon className="mr-2 h-3 w-3" />
                    {item.label}
                    {item.badge && (
                      <Badge variant="outline" className="ml-2 text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                </Button>
              );
            })}
          </nav>
        </div>
      </div>
    </div>
  );
}

// 快速导航卡片组件
export function UserManagementQuickNav() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {navigationItems.map((item) => {
        const Icon = item.icon;
        
        return (
          <Card key={item.path} className="hover:shadow-md transition-shadow cursor-pointer">
            <Link to={item.path}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Icon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{item.label}</h3>
                      {item.badge && (
                        <Badge variant="outline" className="text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {item.description}
                    </p>
                  </div>
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardContent>
            </Link>
          </Card>
        );
      })}
    </div>
  );
}
