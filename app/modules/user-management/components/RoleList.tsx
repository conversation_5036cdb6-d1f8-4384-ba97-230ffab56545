// 角色列表组件
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Shield, 
  Users,
  MoreHorizontal,
  AlertTriangle 
} from 'lucide-react';

import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../../../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../../components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../../components/ui/alert-dialog';
import { Skeleton } from '../../../components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Alert, AlertDescription } from '../../../components/ui/alert';

import { useRoleStore } from '../stores/roleStore';
import { roleUtils, formatUtils } from '../utils';
import type { Role } from '../types';

interface RoleListProps {
  onEditRole?: (role: Role) => void;
  onViewRole?: (role: Role) => void;
}

export function RoleList({ onEditRole, onViewRole }: RoleListProps) {
  const {
    roles,
    pagination,
    tableState,
    loadingState,
    fetchRoles,
    deleteRole,
    setTableState,
  } = useRoleStore();

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null);
  const [searchInput, setSearchInput] = useState(tableState.search);

  // 初始化加载角色列表
  useEffect(() => {
    fetchRoles();
  }, [fetchRoles]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchInput(value);
    setTableState({ search: value, page: 1 });
    fetchRoles({ search: value, page: 1 });
  };

  // 处理分页
  const handlePageChange = (page: number) => {
    setTableState({ page });
    fetchRoles({ page });
  };

  // 处理删除角色
  const handleDeleteRole = async () => {
    if (!roleToDelete) return;
    
    try {
      await deleteRole(roleToDelete.id);
      setDeleteDialogOpen(false);
      setRoleToDelete(null);
    } catch (error) {
      console.error('删除角色失败:', error);
    }
  };

  // 打开删除确认对话框
  const openDeleteDialog = (role: Role) => {
    setRoleToDelete(role);
    setDeleteDialogOpen(true);
  };

  // 检查角色是否可以删除
  const canDeleteRole = (role: Role): boolean => {
    return roleUtils.canDelete(role);
  };

  // 渲染权限数量徽章
  const renderPermissionCount = (role: Role) => {
    const count = roleUtils.getPermissionCount(role);
    return (
      <Badge variant="outline" className="text-xs">
        {count} 个权限
      </Badge>
    );
  };

  // 渲染用户数量徽章
  const renderUserCount = (role: Role) => {
    const count = roleUtils.getUserCount(role);
    return (
      <Badge variant={count > 0 ? "secondary" : "outline"} className="text-xs">
        <Users className="mr-1 h-3 w-3" />
        {count} 个用户
      </Badge>
    );
  };

  // 渲染操作菜单
  const renderActionMenu = (role: Role) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onViewRole?.(role)}>
          <Shield className="mr-2 h-4 w-4" />
          查看详情
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onEditRole?.(role)}>
          <Edit className="mr-2 h-4 w-4" />
          编辑角色
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={() => openDeleteDialog(role)}
          className="text-destructive"
          disabled={!canDeleteRole(role)}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          删除角色
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // 渲染加载骨架
  const renderSkeleton = () => (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <Skeleton className="h-10 w-10 rounded" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">角色管理</h1>
          <p className="text-muted-foreground">
            管理系统角色和权限配置
          </p>
        </div>
        <Button asChild>
          <Link to="/user-management/roles/new">
            <Plus className="mr-2 h-4 w-4" />
            新建角色
          </Link>
        </Button>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">角色列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索角色名称或描述..."
                value={searchInput}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* 角色表格 */}
          {loadingState.loading ? (
            renderSkeleton()
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>角色信息</TableHead>
                    <TableHead>权限数量</TableHead>
                    <TableHead>用户数量</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {roles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium flex items-center">
                            <Shield className="mr-2 h-4 w-4 text-muted-foreground" />
                            {role.name}
                          </div>
                          {role.description && (
                            <div className="text-sm text-muted-foreground">
                              {formatUtils.truncateText(role.description, 60)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {renderPermissionCount(role)}
                      </TableCell>
                      <TableCell>
                        {renderUserCount(role)}
                      </TableCell>
                      <TableCell className="text-sm text-muted-foreground">
                        {formatUtils.formatDate(role.created_at || '')}
                      </TableCell>
                      <TableCell className="text-right">
                        {renderActionMenu(role)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    共 {pagination.total} 个角色，第 {pagination.page} / {pagination.pages} 页
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      上一页
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.pages}
                    >
                      下一页
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 错误提示 */}
          {loadingState.error && (
            <div className="text-center py-8">
              <p className="text-destructive">{loadingState.error}</p>
              <Button 
                variant="outline" 
                onClick={() => fetchRoles()}
                className="mt-2"
              >
                重试
              </Button>
            </div>
          )}

          {/* 空状态 */}
          {!loadingState.loading && roles.length === 0 && !loadingState.error && (
            <div className="text-center py-8">
              <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">暂无角色</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                开始创建第一个角色
              </p>
              <Button asChild className="mt-4">
                <Link to="/user-management/roles/new">
                  <Plus className="mr-2 h-4 w-4" />
                  新建角色
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除角色</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除角色 "{roleToDelete?.name}" 吗？
              {roleToDelete && !canDeleteRole(roleToDelete) && (
                <Alert variant="destructive" className="mt-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    该角色已分配给 {roleUtils.getUserCount(roleToDelete)} 个用户，无法删除。
                    请先取消用户的角色分配。
                  </AlertDescription>
                </Alert>
              )}
              {roleToDelete && canDeleteRole(roleToDelete) && (
                <span className="block mt-2">
                  此操作不可撤销，角色的所有权限配置将被永久删除。
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteRole}
              disabled={roleToDelete ? !canDeleteRole(roleToDelete) : true}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
