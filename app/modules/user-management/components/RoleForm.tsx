// 角色表单组件 - 用于创建和编辑角色
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Save, X } from 'lucide-react';

import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Alert, AlertDescription } from '../../../components/ui/alert';

import { useRoleStore } from '../stores/roleStore';
import { PermissionSelector } from './PermissionSelector';
import { validationUtils } from '../utils';
import { permissionApi } from '../services/api';
import type { Role, RoleFormData } from '../types';

interface RoleFormProps {
  role?: Role | null;
  onSave?: (role: Role) => void;
  onCancel?: () => void;
}

export function RoleForm({ role, onSave, onCancel }: RoleFormProps) {
  const { 
    createRole, 
    updateRole, 
    loadingState, 
    availablePermissions, 
    fetchAvailablePermissions,
    parseRolePermissions 
  } = useRoleStore();
  
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  const isEditing = !!role;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<RoleFormData>({
    defaultValues: {
      name: '',
      description: '',
      permissions: [],
    },
  });

  // 初始化表单数据
  useEffect(() => {
    if (role) {
      reset({
        name: role.name,
        description: role.description,
        permissions: [],
      });
      
      // 解析角色权限
      try {
        const rolePermissions = parseRolePermissions(role);
        const permissionStrings = rolePermissions.map(p => 
          `${p.type}:${p.service}:${p.module}:${p.action}`
        );
        setSelectedPermissions(permissionStrings);
      } catch (error) {
        console.error('解析角色权限失败:', error);
        setSelectedPermissions([]);
      }
    }
  }, [role, reset, parseRolePermissions]);

  // 获取可用权限
  useEffect(() => {
    fetchAvailablePermissions();
  }, [fetchAvailablePermissions]);

  // 处理权限变化
  const handlePermissionChange = (permissions: string[]) => {
    setSelectedPermissions(permissions);
    setValue('permissions', permissions);
  };

  // 处理表单提交
  const onSubmit = async (data: RoleFormData) => {
    try {
      const roleData = {
        ...data,
        permissions: selectedPermissions,
      };

      let savedRole: Role;
      if (isEditing && role) {
        savedRole = await updateRole(role.id, roleData);
      } else {
        savedRole = await createRole(roleData);
      }

      onSave?.(savedRole);
    } catch (error) {
      console.error('保存角色失败:', error);
    }
  };

  // 自定义验证函数
  const validateRoleName = (value: string) => {
    return validationUtils.validateRoleName(value);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">
            {isEditing ? '编辑角色' : '新建角色'}
          </h1>
          <p className="text-muted-foreground">
            {isEditing ? '修改角色信息和权限配置' : '创建新的系统角色'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 角色名称 */}
            <div className="space-y-2">
              <Label htmlFor="name">角色名称 *</Label>
              <Input
                id="name"
                {...register('name', {
                  required: '角色名称不能为空',
                  validate: validateRoleName,
                })}
                placeholder="请输入角色名称"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            {/* 角色描述 */}
            <div className="space-y-2">
              <Label htmlFor="description">角色描述</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="请输入角色描述"
                rows={3}
              />
              <p className="text-xs text-muted-foreground">
                描述该角色的用途和职责范围
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 权限配置 */}
        <PermissionSelector
          availablePermissions={availablePermissions}
          selectedPermissions={selectedPermissions}
          onPermissionChange={handlePermissionChange}
          disabled={loadingState.loading}
        />

        {/* 权限预览 */}
        {selectedPermissions.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>权限预览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  该角色将拥有以下 {selectedPermissions.length} 个权限：
                </p>
                <div className="max-h-32 overflow-y-auto bg-muted/30 rounded p-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-1 text-xs font-mono">
                    {selectedPermissions.map((permission, index) => (
                      <div key={index} className="text-muted-foreground">
                        {permission}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 错误提示 */}
        {loadingState.error && (
          <Alert variant="destructive">
            <AlertDescription>{loadingState.error}</AlertDescription>
          </Alert>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loadingState.loading}
          >
            <X className="mr-2 h-4 w-4" />
            取消
          </Button>
          <Button
            type="submit"
            disabled={loadingState.loading || selectedPermissions.length === 0}
          >
            <Save className="mr-2 h-4 w-4" />
            {loadingState.loading ? '保存中...' : '保存'}
          </Button>
        </div>

        {/* 提示信息 */}
        {selectedPermissions.length === 0 && (
          <Alert>
            <AlertDescription>
              请至少选择一个权限才能保存角色。
            </AlertDescription>
          </Alert>
        )}
      </form>
    </div>
  );
}
