// 用户表单组件 - 用于创建和编辑用户
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Save, X, Eye, EyeOff } from 'lucide-react';

import { But<PERSON> } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Switch } from '../../../components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Separator } from '../../../components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import { Checkbox } from '../../../components/ui/checkbox';
import { Alert, AlertDescription } from '../../../components/ui/alert';

import { useUserStore } from '../stores/userStore';
import { useRoleStore } from '../stores/roleStore';
import { validationUtils } from '../utils';
import type { User, UserFormData } from '../types';

interface UserFormProps {
  user?: User | null;
  onSave?: (user: User) => void;
  onCancel?: () => void;
}

export function UserForm({ user, onSave, onCancel }: UserFormProps) {
  const { createUser, updateUser, loadingState } = useUserStore();
  const { allRoles, fetchAllRoles } = useRoleStore();
  
  const [showPassword, setShowPassword] = useState(false);
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);

  const isEditing = !!user;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<UserFormData>({
    defaultValues: {
      username: '',
      real_name: '',
      is_active: true,
      is_superuser: false,
      password: '',
      role_ids: [],
    },
  });

  // 监听表单值变化
  const watchedValues = watch();

  // 初始化表单数据
  useEffect(() => {
    if (user) {
      reset({
        username: user.username,
        real_name: user.real_name || '',
        is_active: user.is_active,
        is_superuser: user.is_superuser,
        password: '',
        role_ids: user.roles?.map(role => role.id) || [],
      });
      setSelectedRoleIds(user.roles?.map(role => role.id) || []);
    }
  }, [user, reset]);

  // 获取所有角色
  useEffect(() => {
    fetchAllRoles();
  }, [fetchAllRoles]);

  // 处理角色选择
  const handleRoleToggle = (roleId: number, checked: boolean) => {
    const newRoleIds = checked
      ? [...selectedRoleIds, roleId]
      : selectedRoleIds.filter(id => id !== roleId);
    
    setSelectedRoleIds(newRoleIds);
    setValue('role_ids', newRoleIds);
  };

  // 处理表单提交
  const onSubmit = async (data: UserFormData) => {
    try {
      const userData = {
        ...data,
        role_ids: selectedRoleIds,
      };

      let savedUser: User;
      if (isEditing && user) {
        // 编辑模式：如果密码为空，则不更新密码
        const updateData = { ...userData };
        if (!updateData.password) {
          delete updateData.password;
        }
        savedUser = await updateUser(user.id, updateData);
      } else {
        // 创建模式
        savedUser = await createUser(userData);
      }

      onSave?.(savedUser);
    } catch (error) {
      console.error('保存用户失败:', error);
    }
  };

  // 自定义验证函数
  const validateUsername = (value: string) => {
    return validationUtils.validateUsername(value);
  };

  const validatePassword = (value: string) => {
    // 编辑模式下密码可以为空（表示不修改）
    if (isEditing && !value) return null;
    return validationUtils.validatePassword(value);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">
            {isEditing ? '编辑用户' : '新建用户'}
          </h1>
          <p className="text-muted-foreground">
            {isEditing ? '修改用户信息和权限设置' : '创建新的用户账户'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 用户名 */}
            <div className="space-y-2">
              <Label htmlFor="username">用户名 *</Label>
              <Input
                id="username"
                {...register('username', {
                  required: '用户名不能为空',
                  validate: validateUsername,
                })}
                placeholder="请输入用户名"
                disabled={isEditing} // 编辑时不允许修改用户名
              />
              {errors.username && (
                <p className="text-sm text-destructive">{errors.username.message}</p>
              )}
            </div>

            {/* 真实姓名 */}
            <div className="space-y-2">
              <Label htmlFor="real_name">真实姓名</Label>
              <Input
                id="real_name"
                {...register('real_name')}
                placeholder="请输入真实姓名"
              />
            </div>

            {/* 密码 */}
            <div className="space-y-2">
              <Label htmlFor="password">
                密码 {!isEditing && '*'}
                {isEditing && <span className="text-muted-foreground text-sm">（留空表示不修改）</span>}
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  {...register('password', {
                    validate: validatePassword,
                  })}
                  placeholder={isEditing ? '留空表示不修改密码' : '请输入密码'}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {errors.password && (
                <p className="text-sm text-destructive">{errors.password.message}</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 权限设置 */}
        <Card>
          <CardHeader>
            <CardTitle>权限设置</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 账户状态 */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>账户状态</Label>
                <p className="text-sm text-muted-foreground">
                  禁用后用户将无法登录系统
                </p>
              </div>
              <Switch
                checked={watchedValues.is_active}
                onCheckedChange={(checked) => setValue('is_active', checked)}
              />
            </div>

            <Separator />

            {/* 超级管理员 */}
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>超级管理员</Label>
                <p className="text-sm text-muted-foreground">
                  超级管理员拥有所有权限
                </p>
              </div>
              <Switch
                checked={watchedValues.is_superuser}
                onCheckedChange={(checked) => setValue('is_superuser', checked)}
              />
            </div>

            {/* 角色分配 */}
            {!watchedValues.is_superuser && (
              <>
                <Separator />
                <div className="space-y-3">
                  <Label>角色分配</Label>
                  <p className="text-sm text-muted-foreground">
                    为用户分配相应的角色以获得对应权限
                  </p>
                  
                  {allRoles.length === 0 ? (
                    <Alert>
                      <AlertDescription>
                        暂无可用角色，请先创建角色。
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {allRoles.map((role) => (
                        <div key={role.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`role-${role.id}`}
                            checked={selectedRoleIds.includes(role.id)}
                            onCheckedChange={(checked) => 
                              handleRoleToggle(role.id, checked as boolean)
                            }
                          />
                          <div className="grid gap-1.5 leading-none">
                            <Label
                              htmlFor={`role-${role.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {role.name}
                            </Label>
                            {role.description && (
                              <p className="text-xs text-muted-foreground">
                                {role.description}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* 错误提示 */}
        {loadingState.error && (
          <Alert variant="destructive">
            <AlertDescription>{loadingState.error}</AlertDescription>
          </Alert>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loadingState.loading}
          >
            <X className="mr-2 h-4 w-4" />
            取消
          </Button>
          <Button
            type="submit"
            disabled={loadingState.loading}
          >
            <Save className="mr-2 h-4 w-4" />
            {loadingState.loading ? '保存中...' : '保存'}
          </Button>
        </div>
      </form>
    </div>
  );
}
