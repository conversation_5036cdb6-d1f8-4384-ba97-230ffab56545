// 响应式设计辅助组件
import React from 'react';
import { Menu, X } from 'lucide-react';

import { Button } from '../../../components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '../../../components/ui/sheet';
import { useMobile } from '../../../components/ui/use-mobile';

// 响应式容器
interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
}

export function ResponsiveContainer({ children, className = '' }: ResponsiveContainerProps) {
  return (
    <div className={`container mx-auto px-4 sm:px-6 lg:px-8 ${className}`}>
      {children}
    </div>
  );
}

// 响应式网格
interface ResponsiveGridProps {
  children: React.ReactNode;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
  className?: string;
}

export function ResponsiveGrid({ 
  children, 
  cols = { default: 1, md: 2, lg: 3 }, 
  gap = 6,
  className = '' 
}: ResponsiveGridProps) {
  const gridClasses = [
    `grid`,
    `gap-${gap}`,
    cols.default && `grid-cols-${cols.default}`,
    cols.sm && `sm:grid-cols-${cols.sm}`,
    cols.md && `md:grid-cols-${cols.md}`,
    cols.lg && `lg:grid-cols-${cols.lg}`,
    cols.xl && `xl:grid-cols-${cols.xl}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
}

// 移动端侧边栏
interface MobileSidebarProps {
  children: React.ReactNode;
  trigger?: React.ReactNode;
}

export function MobileSidebar({ children, trigger }: MobileSidebarProps) {
  const isMobile = useMobile();

  if (!isMobile) {
    return <>{children}</>;
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        {trigger || (
          <Button variant="outline" size="icon" className="md:hidden">
            <Menu className="h-4 w-4" />
          </Button>
        )}
      </SheetTrigger>
      <SheetContent side="left" className="w-80">
        {children}
      </SheetContent>
    </Sheet>
  );
}

// 响应式表格容器
interface ResponsiveTableProps {
  children: React.ReactNode;
  className?: string;
}

export function ResponsiveTable({ children, className = '' }: ResponsiveTableProps) {
  return (
    <div className={`overflow-x-auto -mx-4 sm:mx-0 ${className}`}>
      <div className="inline-block min-w-full align-middle">
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
          {children}
        </div>
      </div>
    </div>
  );
}

// 响应式卡片布局
interface ResponsiveCardLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  sidebarPosition?: 'left' | 'right';
  className?: string;
}

export function ResponsiveCardLayout({ 
  children, 
  sidebar, 
  sidebarPosition = 'left',
  className = '' 
}: ResponsiveCardLayoutProps) {
  const isMobile = useMobile();

  if (isMobile || !sidebar) {
    return (
      <div className={`space-y-6 ${className}`}>
        {sidebar && (
          <MobileSidebar>
            {sidebar}
          </MobileSidebar>
        )}
        {children}
      </div>
    );
  }

  return (
    <div className={`flex gap-6 ${className}`}>
      {sidebarPosition === 'left' && (
        <div className="w-64 flex-shrink-0">
          {sidebar}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        {children}
      </div>
      
      {sidebarPosition === 'right' && (
        <div className="w-64 flex-shrink-0">
          {sidebar}
        </div>
      )}
    </div>
  );
}

// 响应式操作栏
interface ResponsiveActionBarProps {
  title?: string;
  description?: string;
  actions?: React.ReactNode;
  breadcrumb?: React.ReactNode;
  className?: string;
}

export function ResponsiveActionBar({ 
  title, 
  description, 
  actions, 
  breadcrumb,
  className = '' 
}: ResponsiveActionBarProps) {
  const isMobile = useMobile();

  return (
    <div className={`space-y-4 ${className}`}>
      {breadcrumb && (
        <div className="hidden sm:block">
          {breadcrumb}
        </div>
      )}
      
      <div className={`flex ${isMobile ? 'flex-col space-y-4' : 'items-center justify-between'}`}>
        <div className="min-w-0 flex-1">
          {title && (
            <h1 className="text-2xl font-bold text-gray-900 sm:text-3xl sm:truncate">
              {title}
            </h1>
          )}
          {description && (
            <p className="mt-1 text-sm text-gray-500 sm:text-base">
              {description}
            </p>
          )}
        </div>
        
        {actions && (
          <div className={`flex-shrink-0 ${isMobile ? 'w-full' : ''}`}>
            <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'space-x-3'}`}>
              {actions}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// 响应式表单布局
interface ResponsiveFormLayoutProps {
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export function ResponsiveFormLayout({ 
  children, 
  maxWidth = 'lg',
  className = '' 
}: ResponsiveFormLayoutProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full',
  };

  return (
    <div className={`mx-auto ${maxWidthClasses[maxWidth]} ${className}`}>
      {children}
    </div>
  );
}

// 响应式模态框内容
interface ResponsiveModalContentProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  className?: string;
}

export function ResponsiveModalContent({ 
  children, 
  title, 
  description,
  className = '' 
}: ResponsiveModalContentProps) {
  const isMobile = useMobile();

  return (
    <div className={`${isMobile ? 'px-4 py-6' : 'px-6 py-8'} ${className}`}>
      {(title || description) && (
        <div className="mb-6">
          {title && (
            <h2 className={`font-semibold text-gray-900 ${isMobile ? 'text-lg' : 'text-xl'}`}>
              {title}
            </h2>
          )}
          {description && (
            <p className={`text-gray-500 ${isMobile ? 'text-sm mt-1' : 'text-base mt-2'}`}>
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  );
}

// 响应式分页
interface ResponsivePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
  totalItems?: number;
  className?: string;
}

export function ResponsivePagination({ 
  currentPage, 
  totalPages, 
  onPageChange, 
  showInfo = true,
  totalItems,
  className = '' 
}: ResponsivePaginationProps) {
  const isMobile = useMobile();

  return (
    <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'items-center justify-between'} ${className}`}>
      {showInfo && totalItems && (
        <div className={`text-sm text-gray-700 ${isMobile ? 'text-center' : ''}`}>
          显示第 {((currentPage - 1) * 10) + 1} - {Math.min(currentPage * 10, totalItems)} 项，
          共 {totalItems} 项
        </div>
      )}
      
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
        >
          上一页
        </Button>
        
        {!isMobile && (
          <span className="text-sm text-gray-700">
            第 {currentPage} / {totalPages} 页
          </span>
        )}
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
        >
          下一页
        </Button>
      </div>
    </div>
  );
}
