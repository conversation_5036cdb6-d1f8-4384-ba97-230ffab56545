// 角色详情组件
import React, { useEffect } from 'react';
import { Link } from 'react-router';
import { 
  Edit, 
  Shield, 
  Users, 
  Calendar, 
  ArrowLeft,
  Server,
  Database,
  Key
} from 'lucide-react';

import { But<PERSON> } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Separator } from '../../../components/ui/separator';
import { Skeleton } from '../../../components/ui/skeleton';
import { Alert, AlertDescription } from '../../../components/ui/alert';
import { Avatar, AvatarFallback } from '../../../components/ui/avatar';

import { useRoleStore } from '../stores/roleStore';
import { usePermissionGroups, usePermissionParser } from '../hooks/usePermissions';
import { roleUtils, formatUtils, userUtils } from '../utils';
import type { Role } from '../types';

interface RoleDetailProps {
  roleId: number;
  onEdit?: (role: Role) => void;
}

export function RoleDetail({ roleId, onEdit }: RoleDetailProps) {
  const { selectedRole, loadingState, fetchRole, parseRolePermissions } = useRoleStore();
  const { getPermissionDescription } = usePermissionParser();

  useEffect(() => {
    if (roleId) {
      fetchRole(roleId);
    }
  }, [roleId, fetchRole]);

  // 解析角色权限
  const rolePermissions = selectedRole ? parseRolePermissions(selectedRole) : [];
  const permissionGroups = usePermissionGroups(rolePermissions);

  // 渲染加载状态
  const renderSkeleton = () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-64" />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-24" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-24" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // 渲染角色基本信息
  const renderBasicInfo = (role: Role) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          基本信息
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">角色名称</label>
            <p className="mt-1 text-sm font-medium">{role.name}</p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">权限数量</label>
            <p className="mt-1 text-sm">
              <Badge variant="outline">
                {roleUtils.getPermissionCount(role)} 个权限
              </Badge>
            </p>
          </div>
        </div>

        <div>
          <label className="text-sm font-medium text-muted-foreground">角色描述</label>
          <p className="mt-1 text-sm">{role.description || '暂无描述'}</p>
        </div>

        <Separator />

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">创建时间</label>
            <p className="mt-1 text-sm">
              {formatUtils.formatDate(role.created_at || '')}
            </p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">更新时间</label>
            <p className="mt-1 text-sm">
              {formatUtils.formatDate(role.updated_at || '')}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // 渲染分配用户
  const renderAssignedUsers = (role: Role) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2 h-5 w-5" />
          分配用户
        </CardTitle>
      </CardHeader>
      <CardContent>
        {role.users && role.users.length > 0 ? (
          <div className="space-y-3">
            {role.users.map((user) => (
              <div key={user.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-xs">
                    {userUtils.getDisplayName(user).charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="text-sm font-medium">
                    {userUtils.getDisplayName(user)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    @{user.username}
                  </div>
                </div>
                <Badge 
                  variant={userUtils.getStatusColor(user)}
                  className="text-xs"
                >
                  {userUtils.getStatusText(user)}
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Users className="mx-auto h-8 w-8 text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">
              暂无用户分配到此角色
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  // 渲染权限详情
  const renderPermissionDetails = () => (
    <Card className="lg:col-span-2">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Key className="mr-2 h-5 w-5" />
          权限详情
        </CardTitle>
      </CardHeader>
      <CardContent>
        {permissionGroups.length > 0 ? (
          <div className="space-y-4">
            {permissionGroups.map((group) => (
              <div key={group.service} className="border rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Server className="h-4 w-4 text-muted-foreground" />
                  <h4 className="font-medium">{group.service}</h4>
                  <Badge variant="outline" className="text-xs">
                    {Object.values(group.modules).flat().length} 个权限
                  </Badge>
                </div>
                
                <div className="space-y-3">
                  {Object.entries(group.modules).map(([moduleName, permissions]) => (
                    <div key={moduleName} className="border-l-2 border-muted pl-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Database className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm font-medium">{moduleName}</span>
                        <Badge variant="outline" className="text-xs">
                          {permissions.length} 个权限
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {permissions.map((permission, index) => (
                          <div key={index} className="flex items-center space-x-2 text-sm">
                            <Badge variant="secondary" className="text-xs">
                              {permission.action}
                            </Badge>
                            <span className="text-muted-foreground">
                              {getPermissionDescription(permission)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <Key className="mx-auto h-8 w-8 text-muted-foreground" />
            <p className="mt-2 text-sm text-muted-foreground">
              该角色暂无权限配置
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (loadingState.loading) {
    return renderSkeleton();
  }

  if (loadingState.error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="ghost" asChild>
            <Link to="/user-management/roles">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回角色列表
            </Link>
          </Button>
        </div>
        
        <Alert variant="destructive">
          <AlertDescription>{loadingState.error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!selectedRole) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="ghost" asChild>
            <Link to="/user-management/roles">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回角色列表
            </Link>
          </Button>
        </div>
        
        <Alert>
          <AlertDescription>角色不存在或已被删除</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" asChild>
            <Link to="/user-management/roles">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回角色列表
            </Link>
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">{selectedRole.name}</h1>
            <p className="text-muted-foreground">
              角色详细信息和权限配置
            </p>
          </div>
        </div>

        <Button onClick={() => onEdit?.(selectedRole)}>
          <Edit className="mr-2 h-4 w-4" />
          编辑角色
        </Button>
      </div>

      {/* 角色信息卡片 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderBasicInfo(selectedRole)}
        {renderAssignedUsers(selectedRole)}
        {renderPermissionDetails()}
      </div>
    </div>
  );
}
