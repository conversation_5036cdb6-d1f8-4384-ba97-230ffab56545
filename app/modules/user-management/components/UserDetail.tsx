// 用户详情组件
import React, { useEffect } from 'react';
import { Link } from 'react-router';
import { 
  Edit, 
  Shield, 
  User, 
  Calendar, 
  CheckCircle, 
  XCircle,
  ArrowLeft 
} from 'lucide-react';

import { But<PERSON> } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Separator } from '../../../components/ui/separator';
import { Skeleton } from '../../../components/ui/skeleton';
import { Alert, AlertDescription } from '../../../components/ui/alert';

import { useUserStore } from '../stores/userStore';
import { userUtils, formatUtils } from '../utils';
import type { User } from '../types';

interface UserDetailProps {
  userId: number;
  onEdit?: (user: User) => void;
}

export function UserDetail({ userId, onEdit }: UserDetailProps) {
  const { selectedUser, loadingState, fetchUser } = useUserStore();

  useEffect(() => {
    if (userId) {
      fetchUser(userId);
    }
  }, [userId, fetchUser]);

  // 渲染加载状态
  const renderSkeleton = () => (
    <div className="space-y-6">
      <div className="space-y-2">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-64" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-24" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-24" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
        </Card>
      </div>
    </div>
  );

  // 渲染用户基本信息
  const renderBasicInfo = (user: User) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <User className="mr-2 h-5 w-5" />
          基本信息
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">用户名</label>
            <p className="mt-1 text-sm">{user.username}</p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">真实姓名</label>
            <p className="mt-1 text-sm">{user.real_name || '-'}</p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">Lark Open ID</label>
            <p className="mt-1 text-sm">{user.lark_open_id || '-'}</p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">账户状态</label>
            <div className="mt-1">
              <Badge 
                variant={userUtils.getStatusColor(user)}
                className="text-xs"
              >
                {user.is_active ? (
                  <CheckCircle className="mr-1 h-3 w-3" />
                ) : (
                  <XCircle className="mr-1 h-3 w-3" />
                )}
                {userUtils.getStatusText(user)}
              </Badge>
            </div>
          </div>
        </div>

        <Separator />

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">创建时间</label>
            <p className="mt-1 text-sm">
              {formatUtils.formatDate(user.created_at || '')}
            </p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">更新时间</label>
            <p className="mt-1 text-sm">
              {formatUtils.formatDate(user.updated_at || '')}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // 渲染权限信息
  const renderPermissionInfo = (user: User) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="mr-2 h-5 w-5" />
          权限信息
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 超级管理员状态 */}
        <div>
          <label className="text-sm font-medium text-muted-foreground">管理员权限</label>
          <div className="mt-1">
            <Badge variant={user.is_superuser ? "secondary" : "outline"}>
              {user.is_superuser ? '超级管理员' : '普通用户'}
            </Badge>
          </div>
        </div>

        <Separator />

        {/* 角色列表 */}
        <div>
          <label className="text-sm font-medium text-muted-foreground">分配角色</label>
          <div className="mt-2">
            {user.roles && user.roles.length > 0 ? (
              <div className="space-y-2">
                {user.roles.map((role) => (
                  <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <h4 className="text-sm font-medium">{role.name}</h4>
                      {role.description && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {role.description}
                        </p>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {role.permissions ? role.permissions.split(',').length : 0} 个权限
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">未分配任何角色</p>
            )}
          </div>
        </div>

        {/* 权限统计 */}
        {user.roles && user.roles.length > 0 && (
          <>
            <Separator />
            <div>
              <label className="text-sm font-medium text-muted-foreground">权限统计</label>
              <div className="mt-2 grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-primary">
                    {user.roles.length}
                  </div>
                  <div className="text-xs text-muted-foreground">分配角色</div>
                </div>
                <div className="text-center p-3 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-primary">
                    {userUtils.getAllPermissions(user).length}
                  </div>
                  <div className="text-xs text-muted-foreground">总权限数</div>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );

  if (loadingState.loading) {
    return renderSkeleton();
  }

  if (loadingState.error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="ghost" asChild>
            <Link to="/user-management/users">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回用户列表
            </Link>
          </Button>
        </div>
        
        <Alert variant="destructive">
          <AlertDescription>{loadingState.error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!selectedUser) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button variant="ghost" asChild>
            <Link to="/user-management/users">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回用户列表
            </Link>
          </Button>
        </div>
        
        <Alert>
          <AlertDescription>用户不存在或已被删除</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" asChild>
            <Link to="/user-management/users">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回用户列表
            </Link>
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold">
              {userUtils.getDisplayName(selectedUser)}
            </h1>
            <p className="text-muted-foreground">
              用户详细信息和权限配置
            </p>
          </div>
        </div>

        <Button onClick={() => onEdit?.(selectedUser)}>
          <Edit className="mr-2 h-4 w-4" />
          编辑用户
        </Button>
      </div>

      {/* 用户信息卡片 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderBasicInfo(selectedUser)}
        {renderPermissionInfo(selectedUser)}
      </div>
    </div>
  );
}
