// 用户角色权限管理模块的工具函数
import type { User, Role, Permission } from '../types';

// 用户相关工具函数
export const userUtils = {
  // 获取用户显示名称
  getDisplayName: (user: User): string => {
    return user.real_name || user.username;
  },

  // 获取用户状态文本
  getStatusText: (user: User): string => {
    if (!user.is_active) return '已禁用';
    if (user.is_superuser) return '超级管理员';
    return '正常';
  },

  // 获取用户状态颜色
  getStatusColor: (user: User): 'default' | 'secondary' | 'destructive' => {
    if (!user.is_active) return 'destructive';
    if (user.is_superuser) return 'secondary';
    return 'default';
  },

  // 获取用户角色名称列表
  getRoleNames: (user: User): string[] => {
    return user.roles?.map(role => role.name) || [];
  },

  // 检查用户是否有特定角色
  hasRole: (user: User, roleName: string): boolean => {
    return user.roles?.some(role => role.name === roleName) || false;
  },

  // 获取用户所有权限
  getAllPermissions: (user: User): string[] => {
    const permissions = new Set<string>();
    
    user.roles?.forEach(role => {
      if (role.permissions) {
        role.permissions.split(',').forEach(perm => {
          permissions.add(perm.trim());
        });
      }
    });

    return Array.from(permissions);
  },
};

// 角色相关工具函数
export const roleUtils = {
  // 获取角色权限数量
  getPermissionCount: (role: Role): number => {
    if (!role.permissions) return 0;
    return role.permissions.split(',').filter(p => p.trim()).length;
  },

  // 获取角色用户数量
  getUserCount: (role: Role): number => {
    return role.users?.length || 0;
  },

  // 检查角色是否包含特定权限
  hasPermission: (role: Role, permission: string): boolean => {
    if (!role.permissions) return false;
    return role.permissions.split(',').some(p => p.trim() === permission);
  },

  // 获取角色的权限列表
  getPermissionList: (role: Role): string[] => {
    if (!role.permissions) return [];
    return role.permissions.split(',').map(p => p.trim()).filter(p => p);
  },

  // 检查角色是否可以删除（没有用户分配）
  canDelete: (role: Role): boolean => {
    return (role.users?.length || 0) === 0;
  },
};

// 权限相关工具函数
export const permissionUtils = {
  // 解析权限字符串
  parsePermission: (permissionString: string): Permission | null => {
    const parts = permissionString.split(':');
    if (parts.length !== 4) return null;

    return {
      type: parts[0] as any,
      service: parts[1],
      module: parts[2],
      action: parts[3] as any,
    };
  },

  // 格式化权限显示
  formatPermission: (permission: Permission): string => {
    return `${permission.type}:${permission.service}:${permission.module}:${permission.action}`;
  },

  // 获取权限的友好显示名称
  getPermissionDisplayName: (permission: Permission): string => {
    const typeMap: { [key: string]: string } = {
      'api': 'API',
      'field': '字段',
      'record': '记录',
    };

    const actionMap: { [key: string]: string } = {
      'read': '读取',
      'write': '写入',
      'write.create': '创建',
      'write.update': '更新',
      'write.delete': '删除',
    };

    return `${typeMap[permission.type] || permission.type} - ${permission.service}/${permission.module} - ${actionMap[permission.action] || permission.action}`;
  },

  // 按服务分组权限
  groupByService: (permissions: Permission[]): { [service: string]: Permission[] } => {
    return permissions.reduce((groups, permission) => {
      if (!groups[permission.service]) {
        groups[permission.service] = [];
      }
      groups[permission.service].push(permission);
      return groups;
    }, {} as { [service: string]: Permission[] });
  },

  // 按模块分组权限
  groupByModule: (permissions: Permission[]): { [module: string]: Permission[] } => {
    return permissions.reduce((groups, permission) => {
      const key = `${permission.service}:${permission.module}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(permission);
      return groups;
    }, {} as { [module: string]: Permission[] });
  },

  // 检查权限是否冲突
  hasConflict: (permissions: Permission[]): boolean => {
    // 检查是否同时有读写权限冲突
    const permissionStrings = permissions.map(p => permissionUtils.formatPermission(p));
    const uniquePermissions = new Set(permissionStrings);
    return uniquePermissions.size !== permissionStrings.length;
  },
};

// 表单验证工具函数
export const validationUtils = {
  // 验证用户名
  validateUsername: (username: string): string | null => {
    if (!username) return '用户名不能为空';
    if (username.length < 3) return '用户名至少3个字符';
    if (username.length > 50) return '用户名不能超过50个字符';
    if (!/^[a-zA-Z0-9_-]+$/.test(username)) return '用户名只能包含字母、数字、下划线和连字符';
    return null;
  },

  // 验证角色名称
  validateRoleName: (name: string): string | null => {
    if (!name) return '角色名称不能为空';
    if (name.length < 2) return '角色名称至少2个字符';
    if (name.length > 100) return '角色名称不能超过100个字符';
    return null;
  },

  // 验证密码
  validatePassword: (password: string): string | null => {
    if (!password) return '密码不能为空';
    if (password.length < 6) return '密码至少6个字符';
    if (password.length > 128) return '密码不能超过128个字符';
    return null;
  },

  // 验证邮箱
  validateEmail: (email: string): string | null => {
    if (!email) return null; // 邮箱可选
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return '邮箱格式不正确';
    return null;
  },
};

// 格式化工具函数
export const formatUtils = {
  // 格式化日期
  formatDate: (dateString: string): string => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  },

  // 格式化相对时间
  formatRelativeTime: (dateString: string): string => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return '今天';
    if (diffDays === 1) return '昨天';
    if (diffDays < 7) return `${diffDays}天前`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`;
    return `${Math.floor(diffDays / 365)}年前`;
  },

  // 截断文本
  truncateText: (text: string, maxLength: number): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  },
};
