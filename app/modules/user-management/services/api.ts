// API服务层 - 处理所有与后端的数据交互
import axios from 'axios';
import type {
  User,
  Role,
  Permission,
  CreateUserRequest,
  UpdateUserRequest,
  CreateRoleRequest,
  UpdateRoleRequest,
  AssignRoleRequest,
  PaginatedResponse,
  PaginationParams,
} from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一错误处理
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // 处理未授权错误
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 用户管理API
export const userApi = {
  // 获取用户列表
  getUsers: async (params: PaginationParams = {}): Promise<PaginatedResponse<User>> => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  // 获取单个用户
  getUser: async (id: number): Promise<User> => {
    const response = await api.get(`/users/${id}`);
    return response.data;
  },

  // 创建用户
  createUser: async (data: CreateUserRequest): Promise<User> => {
    const response = await api.post('/users', data);
    return response.data;
  },

  // 更新用户
  updateUser: async (id: number, data: UpdateUserRequest): Promise<User> => {
    const response = await api.put(`/users/${id}`, data);
    return response.data;
  },

  // 删除用户
  deleteUser: async (id: number): Promise<void> => {
    await api.delete(`/users/${id}`);
  },

  // 分配角色给用户
  assignRoles: async (data: AssignRoleRequest): Promise<void> => {
    await api.post('/users/assign-roles', data);
  },

  // 获取用户的角色
  getUserRoles: async (userId: number): Promise<Role[]> => {
    const response = await api.get(`/users/${userId}/roles`);
    return response.data;
  },
};

// 角色管理API
export const roleApi = {
  // 获取角色列表
  getRoles: async (params: PaginationParams = {}): Promise<PaginatedResponse<Role>> => {
    const response = await api.get('/roles', { params });
    return response.data;
  },

  // 获取所有角色（不分页）
  getAllRoles: async (): Promise<Role[]> => {
    const response = await api.get('/roles/all');
    return response.data;
  },

  // 获取单个角色
  getRole: async (id: number): Promise<Role> => {
    const response = await api.get(`/roles/${id}`);
    return response.data;
  },

  // 创建角色
  createRole: async (data: CreateRoleRequest): Promise<Role> => {
    const response = await api.post('/roles', data);
    return response.data;
  },

  // 更新角色
  updateRole: async (id: number, data: UpdateRoleRequest): Promise<Role> => {
    const response = await api.put(`/roles/${id}`, data);
    return response.data;
  },

  // 删除角色
  deleteRole: async (id: number): Promise<void> => {
    await api.delete(`/roles/${id}`);
  },

  // 获取角色的用户
  getRoleUsers: async (roleId: number): Promise<User[]> => {
    const response = await api.get(`/roles/${roleId}/users`);
    return response.data;
  },
};

// 权限管理API
export const permissionApi = {
  // 获取所有可用权限
  getAvailablePermissions: async (): Promise<Permission[]> => {
    const response = await api.get('/permissions');
    return response.data;
  },

  // 解析权限字符串为权限对象
  parsePermissions: (permissionString: string): Permission[] => {
    if (!permissionString) return [];
    
    return permissionString.split(',').map(perm => {
      const parts = perm.trim().split(':');
      if (parts.length !== 4) {
        throw new Error(`Invalid permission format: ${perm}`);
      }
      
      return {
        type: parts[0] as any,
        service: parts[1],
        module: parts[2],
        action: parts[3] as any,
      };
    });
  },

  // 将权限对象数组转换为权限字符串
  stringifyPermissions: (permissions: Permission[]): string => {
    return permissions.map(perm => 
      `${perm.type}:${perm.service}:${perm.module}:${perm.action}`
    ).join(',');
  },
};

export default api;
