// 编辑用户页面路由
import { useNavigate, useParams } from "react-router";
import { useEffect } from "react";
import type { Route } from "./+types/user-management.users.$userId.edit";
import { UserForm } from "../modules/user-management/components/UserForm";
import { useUserStore } from "../modules/user-management/stores/userStore";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `编辑用户 - 数据门户` },
    { name: "description", content: "编辑用户信息" },
  ];
}

export default function EditUser() {
  const navigate = useNavigate();
  const { userId } = useParams();
  const { selectedUser, fetchUser } = useUserStore();

  useEffect(() => {
    if (userId) {
      fetchUser(parseInt(userId));
    }
  }, [userId, fetchUser]);

  const handleSave = () => {
    // 保存成功后跳转到用户详情页
    navigate(`/user-management/users/${userId}`);
  };

  const handleCancel = () => {
    // 取消后返回用户详情页
    navigate(`/user-management/users/${userId}`);
  };

  if (!userId) {
    return <div>用户ID无效</div>;
  }

  return (
    <UserForm
      user={selectedUser}
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
}
