// 编辑角色页面路由
import { useNavigate, useParams } from "react-router";
import { useEffect } from "react";
import type { Route } from "./+types/user-management.roles.$roleId.edit";
import { RoleForm } from "../modules/user-management/components/RoleForm";
import { useRoleStore } from "../modules/user-management/stores/roleStore";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `编辑角色 - 数据门户` },
    { name: "description", content: "编辑角色信息" },
  ];
}

export default function EditRole() {
  const navigate = useNavigate();
  const { roleId } = useParams();
  const { selectedRole, fetchRole } = useRoleStore();

  useEffect(() => {
    if (roleId) {
      fetchRole(parseInt(roleId));
    }
  }, [roleId, fetchRole]);

  const handleSave = () => {
    // 保存成功后跳转到角色详情页
    navigate(`/user-management/roles/${roleId}`);
  };

  const handleCancel = () => {
    // 取消后返回角色详情页
    navigate(`/user-management/roles/${roleId}`);
  };

  if (!roleId) {
    return <div>角色ID无效</div>;
  }

  return (
    <RoleForm
      role={selectedRole}
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
}
