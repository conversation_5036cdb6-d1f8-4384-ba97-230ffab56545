// 用户管理模块布局路由
import { Outlet } from "react-router";
import { UserManagementTopNav, UserManagementBreadcrumb } from "../modules/user-management/components/Navigation";

export default function UserManagementLayout() {
  return (
    <div className="min-h-screen bg-background">
      {/* 顶部导航 */}
      <UserManagementTopNav />
      
      {/* 主内容区域 */}
      <div className="container mx-auto py-6 space-y-4">
        {/* 面包屑导航 */}
        <UserManagementBreadcrumb />
        
        {/* 页面内容 */}
        <Outlet />
      </div>
    </div>
  );
}
