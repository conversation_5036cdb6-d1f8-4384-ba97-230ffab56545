// 新建用户页面路由
import { useNavigate } from "react-router";
import type { Route } from "./+types/user-management.users.new";
import { UserForm } from "../modules/user-management/components/UserForm";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "新建用户 - 数据门户" },
    { name: "description", content: "创建新的用户账户" },
  ];
}

export default function NewUser() {
  const navigate = useNavigate();

  const handleSave = () => {
    // 保存成功后跳转到用户列表
    navigate("/user-management/users");
  };

  const handleCancel = () => {
    // 取消后返回用户列表
    navigate("/user-management/users");
  };

  return (
    <UserForm
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
}
