// 新建角色页面路由
import { useNavigate } from "react-router";
import type { Route } from "./+types/user-management.roles.new";
import { RoleForm } from "../modules/user-management/components/RoleForm";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "新建角色 - 数据门户" },
    { name: "description", content: "创建新的用户角色" },
  ];
}

export default function NewRole() {
  const navigate = useNavigate();

  const handleSave = () => {
    // 保存成功后跳转到角色列表
    navigate("/user-management/roles");
  };

  const handleCancel = () => {
    // 取消后返回角色列表
    navigate("/user-management/roles");
  };

  return (
    <RoleForm
      onSave={handleSave}
      onCancel={handleCancel}
    />
  );
}
