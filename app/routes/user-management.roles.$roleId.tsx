// 角色详情页面路由
import { useNavigate, useParams } from "react-router";
import type { Route } from "./+types/user-management.roles.$roleId";
import { RoleDetail } from "../modules/user-management/components/RoleDetail";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `角色详情 - 数据门户` },
    { name: "description", content: "查看角色详细信息" },
  ];
}

export default function RoleDetailPage() {
  const navigate = useNavigate();
  const { roleId } = useParams();

  const handleEdit = () => {
    // 跳转到编辑页面
    navigate(`/user-management/roles/${roleId}/edit`);
  };

  if (!roleId) {
    return <div>角色ID无效</div>;
  }

  return (
    <RoleDetail
      roleId={parseInt(roleId)}
      onEdit={handleEdit}
    />
  );
}
