// 用户详情页面路由
import { useNavigate, useParams } from "react-router";
import type { Route } from "./+types/user-management.users.$userId";
import { UserDetail } from "../modules/user-management/components/UserDetail";

export function meta({ params }: Route.MetaArgs) {
  return [
    { title: `用户详情 - 数据门户` },
    { name: "description", content: "查看用户详细信息" },
  ];
}

export default function UserDetailPage() {
  const navigate = useNavigate();
  const { userId } = useParams();

  const handleEdit = () => {
    // 跳转到编辑页面
    navigate(`/user-management/users/${userId}/edit`);
  };

  if (!userId) {
    return <div>用户ID无效</div>;
  }

  return (
    <UserDetail
      userId={parseInt(userId)}
      onEdit={handleEdit}
    />
  );
}
